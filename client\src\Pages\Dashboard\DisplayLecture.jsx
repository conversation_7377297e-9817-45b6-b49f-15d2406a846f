import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  getCourseLectures,
  deleteCourseLecture,
} from "../../Redux/Slices/LectureSlice";
import { submitTest, getTestSubmission } from "../../Redux/Slices/TestSubmissionSlice";
import Layout from "../../Layout/Layout";
import toast from "react-hot-toast";
import './you.css'

export default function DisplayLecture() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { state } = useLocation();
  const { lectures } = useSelector((state) => state.lecture);
  const { role } = useSelector((state) => state.auth);
  const { hasSubmitted, currentSubmission, isLoading } = useSelector((state) => state.testSubmission);

  const [currentVideo, setCurrentVideo] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [showResults, setShowResults] = useState(false);
  const [score, setScore] = useState(0);
  const [testSubmissions, setTestSubmissions] = useState({});

  // Reset quiz state when changing lectures
  const handleLectureChange = async (idx) => {
    setCurrentVideo(idx);
    setSelectedAnswers({});
    setShowResults(false);
    setScore(0);

    // Check if this lecture's test has been submitted
    if (lectures[idx]?._id) {
      await checkTestSubmission(lectures[idx]._id);
    }
  };

  // Check if test has been submitted for current lecture
  const checkTestSubmission = async (lectureId) => {
    try {
      const result = await dispatch(getTestSubmission({
        courseId: state._id,
        lectureId
      })).unwrap();

      if (result.hasSubmitted) {
        setTestSubmissions(prev => ({
          ...prev,
          [lectureId]: result.submission
        }));
        setShowResults(true);
        setScore(result.submission.score);
      }
    } catch (error) {
      console.error('Error checking test submission:', error);
    }
  };

  async function onLectureDelete(courseId, lectureId) {
    await dispatch(
      deleteCourseLecture({ courseId: courseId, lectureId: lectureId })
    );
    await dispatch(getCourseLectures(courseId));
  }

  useEffect(() => {
    if (!state) navigate("/courses");
    dispatch(getCourseLectures(state._id));
  }, []);

  useEffect(() => {
    // Check test submission for initial lecture when lectures are loaded
    if (lectures.length > 0 && lectures[currentVideo]?._id) {
      checkTestSubmission(lectures[currentVideo]._id);
    }
  }, [lectures, currentVideo]);

  const handlePdfDownload = (secure_url) => {
    window.open(secure_url, '_blank');
  };

  const handleOptionSelect = (questionIndex, optionIndex) => {
    setSelectedAnswers({
      ...selectedAnswers,
      [questionIndex]: optionIndex
    });
  };

  const handleSubmitQuiz = async () => {
    const currentQuestions = lectures[currentVideo]?.questions || [];
    const currentLectureId = lectures[currentVideo]?._id;

    // Check if test has already been submitted
    if (testSubmissions[currentLectureId]) {
      toast.error('You have already submitted this test. Retakes are not allowed.');
      return;
    }

    // Check if all questions are answered
    const unansweredQuestions = currentQuestions.filter((_, index) =>
      selectedAnswers[index] === undefined
    );

    if (unansweredQuestions.length > 0) {
      toast.error(`Please answer all questions before submitting. ${unansweredQuestions.length} question${unansweredQuestions.length > 1 ? 's' : ''} remaining.`);
      return;
    }

    // Prepare answers for submission
    const answers = currentQuestions.map((question, index) => ({
      selectedOption: selectedAnswers[index]
    }));

    try {
      const result = await dispatch(submitTest({
        courseId: state._id,
        lectureId: currentLectureId,
        answers
      })).unwrap();

      setScore(result.submission.score);
      setShowResults(true);
      setTestSubmissions(prev => ({
        ...prev,
        [currentLectureId]: result.submission
      }));

      toast.success('Test submitted successfully!');
    } catch (error) {
      toast.error(error.message || 'Failed to submit test');
    }
  };

  const handleRetakeQuiz = () => {
    const currentLectureId = lectures[currentVideo]?._id;

    // Check if test has been submitted - if so, don't allow retake
    if (testSubmissions[currentLectureId]) {
      toast.error('You have already submitted this test. Retakes are not allowed.');
      return;
    }

    setSelectedAnswers({});
    setShowResults(false);
    setScore(0);
  };

  return (
    <Layout hideFooter={true} hideNav={true} hideBar={true}>
      <section className="flex flex-col gap-6 items-center py-8 px-4">
        {/* Main content section */}
        <div className="flex flex-col dark:bg-base-100 relative md:gap-12 gap-5 rounded-lg md:py-10 md:pt-3 py-4 pt-3 md:px-7 px-4 md:w-[780px] w-full shadow-custom dark:shadow-xl">
          <h1 className="text-center relative md:px-0 px-3 w-fit dark:text-purple-500 md:text-2xl text-lg font-bold font-inter after:content-[' '] after:absolute after:-bottom-2 md:after:left-0 after:left-3 after:h-[3px] after:w-[60%] after:rounded-full after:bg-yellow-400 dark:after:bg-yellow-600">
            Course:{" "}
            <span className="text-violet-500 dark:text-yellow-500 font-nunito-sans">
              {state?.title}
            </span>
          </h1>

          <div className="flex md:flex-row flex-col md:justify-between w-full">
            {/* Left section for lecture video and details */}
            <div className="md:w-[48%] w-full md:p-3 p-1">
              <div className="w-full">

                {/* Display Vimeo video */}
                <div className="vimeo-player">
                  {lectures && lectures?.[currentVideo]?.vimeoVideoId ? (
                    <iframe
                      src={`https://player.vimeo.com/video/${lectures[currentVideo].vimeoVideoId}?badge=0&autopause=0&player_id=0&app_id=58479`}
                      title={lectures[currentVideo].title}
                      frameBorder="0"
                      allow="autoplay; fullscreen; picture-in-picture"
                      allowFullScreen
                      className="w-full h-[170px] mx-auto rounded-md"
                    ></iframe>
                  ) : (
                    <div className="w-full h-[170px] mx-auto rounded-md bg-gray-200 flex items-center justify-center">
                      <p className="text-gray-500">No video available</p>
                    </div>
                  )}
                </div>
                <div className="py-7">
                  <h1 className="text-[17px] text-gray-700 font-[500] dark:text-white font-lato">
                    <span className="text-blue-500 dark:text-yellow-500 font-inter font-semibold text-lg">
                      Title:{" "}
                    </span>
                    {lectures && lectures?.[currentVideo]?.title}
                  </h1>
                  <p className="text-[16.5px] pb-5 text-gray-700 font-[500] dark:text-slate-300 font-lato">
                    <span className="text-blue-500 dark:text-yellow-500 font-inter font-semibold text-lg">
                      Description:{" "}
                    </span>
                    {lectures && lectures?.[currentVideo]?.description}
                  </p>

                  <p className="text-[16.5px] text-gray-700 font-[500] dark:text-slate-300 font-lato">
                    <span className="text-blue-500 dark:text-yellow-500 font-inter font-semibold text-lg">
                      Lecture pdf:{" "}
                    </span>
                    <a href={lectures && lectures?.[currentVideo]?.link} target="_blank" rel="noopener noreferrer" className="text-blue-500">
                      See PDF
                    </a>
                  </p>
                </div>
              </div>
            </div>

            {/* Right section for lectures list */}
            <div className="md:w-[48%] w-full max-h-[500px] overflow-y-auto">
              <ul className="w-full md:p-2 p-0 flex flex-col gap-5 shadow-sm">
                <li className="font-semibold bg-slate-50 dark:bg-slate-100 p-3 rounded-md shadow-lg sticky top-0 text-xl text-[#2320f7] font-nunito-sans flex items-center justify-between">
                  <p>Lectures list</p>
                  {role === "ADMIN" && (
                    <button
                      onClick={() =>
                        navigate("/course/addlecture", { state: { ...state } })
                      }
                      className="btn-primary px-3 py-2 font-inter rounded-md font-semibold text-sm"
                    >
                      Add new lecture
                    </button>
                  )}
                </li>
                {lectures &&
                  lectures.map((lecture, idx) => (
                    <li className="space-y-2" key={lecture._id}>
                      <div className="flex flex-col gap-2">
                        <p
                          className={`cursor-pointer text-base font-[500] font-open-sans ${
                            currentVideo === idx
                              ? "text-blue-600 dark:text-yellow-500"
                              : "text-gray-600 dark:text-white"
                          }`}
                          onClick={() => handleLectureChange(idx)}
                        >
                          <span className="font-inter">{idx + 1}. </span>
                          {lecture?.title}
                        </p>

                        {lecture?.materials?.secure_url && (
                          <button
                            onClick={() => handlePdfDownload(lecture.materials.secure_url)}
                            className="bg-green-500 hover:bg-green-600 px-3 py-1 rounded-md text-white font-inter font-[500] text-sm w-fit flex items-center gap-2"
                          >
                            Download PDF
                          </button>
                        )}

                        {role === "ADMIN" && (
                          <button
                            onClick={() => onLectureDelete(state?._id, lecture?._id)}
                            className="bg-[#ff3838] px-2 py-1 rounded-md text-white font-inter font-[500] text-sm w-fit"
                          >
                            Delete lecture
                          </button>
                        )}
                      </div>
                    </li>
                  ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Questions Section */}
        <div className="flex flex-col dark:bg-base-100 rounded-lg md:py-8 py-4 md:px-7 px-4 md:w-[780px] w-full shadow-custom dark:shadow-xl">
          <h2 className="text-center text-2xl font-bold text-blue-500 dark:text-yellow-500 font-inter mb-6">
            Practice Questions
          </h2>

          {lectures?.[currentVideo]?.questions?.length > 0 ? (
            <>
              <div className="space-y-6">
                {lectures[currentVideo].questions.map((question, qIndex) => (
                  <div
                    key={qIndex}
                    className="border dark:border-gray-700 rounded-lg p-6 bg-white dark:bg-gray-800 shadow-lg"
                  >
                    <h3 className="text-[17px] font-[500] text-gray-700 dark:text-white font-lato mb-4">
                      <span className="text-blue-500 dark:text-yellow-500 font-inter font-semibold">
                        Question {qIndex + 1}:{" "}
                      </span>
                      {question.questionText}
                    </h3>

                    <div className="space-y-3 pl-4">
                      {question.options.map((option, oIndex) => (
                        <div
                          key={oIndex}
                          className={`flex items-center gap-3 p-3 border dark:border-gray-700 rounded-md cursor-pointer
                            ${showResults
                              ? oIndex === question.correctOption
                                ? 'bg-green-100 dark:bg-green-900'
                                : selectedAnswers[qIndex] === oIndex
                                  ? 'bg-red-100 dark:bg-red-900'
                                  : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                              : selectedAnswers[qIndex] === oIndex
                                ? 'bg-blue-50 dark:bg-blue-900'
                                : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                            }`}
                          onClick={() => !showResults && handleOptionSelect(qIndex, oIndex)}
                        >
                          <input
                            type="radio"
                            name={`question-${qIndex}`}
                            checked={selectedAnswers[qIndex] === oIndex}
                            onChange={() => !showResults && handleOptionSelect(qIndex, oIndex)}
                            className="w-4 h-4"
                            disabled={showResults}
                          />
                          <label className="cursor-pointer text-gray-600 dark:text-gray-300 font-lato">
                            {option}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-8 flex flex-col items-center gap-4">
                {showResults && (
                  <div className="text-center mb-4">
                    <p className="text-xl font-semibold text-gray-700 dark:text-gray-300">
                      Your Score: {score.toFixed(1)}%
                    </p>
                    {testSubmissions[lectures[currentVideo]?._id] && (
                      <p className="text-sm text-red-600 dark:text-red-400 mt-2">
                        Test submitted on: {new Date(testSubmissions[lectures[currentVideo]._id].submissionDate).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                )}

                {testSubmissions[lectures[currentVideo]?._id] ? (
                  <div className="text-center">
                    <p className="text-red-600 dark:text-red-400 font-semibold mb-4">
                      You have already submitted this test. Retakes are not allowed.
                    </p>
                    <button
                      disabled
                      className="px-6 py-3 rounded-lg font-semibold text-white bg-gray-400 cursor-not-allowed"
                    >
                      Test Completed
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={showResults ? handleRetakeQuiz : handleSubmitQuiz}
                    disabled={isLoading}
                    className={`px-6 py-3 rounded-lg font-semibold text-white transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed
                      ${showResults
                        ? 'bg-yellow-500 hover:bg-yellow-600'
                        : 'bg-violet-500 hover:bg-violet-600'
                      }`}
                  >
                    {isLoading ? 'Submitting...' : showResults ? 'Retake Quiz' : 'Submit Answers'}
                  </button>
                )}
              </div>
            </>
          ) : (
            <p className="text-center text-gray-500 dark:text-gray-400 font-lato py-4">
              No questions available for this lecture.
            </p>
          )}
        </div>
      </section>
    </Layout>
  );
}