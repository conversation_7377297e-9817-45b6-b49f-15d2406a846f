{"name": "@vimeo/player", "version": "2.29.2", "description": "Interact with and control an embedded Vimeo Player.", "license": "MIT", "author": "Vimeo <https://vimeo.com>", "scripts": {"clean": "rimraf dist/*", "prebuild": "npm run clean -s", "build": "cross-env BABEL_ENV=production node rollup.js", "lint": "eslint src/ test/", "test": "nyc --cache --reporter=text --reporter=html ava", "report-coverage": "nyc report --reporter=text-lcov > coverage.lcov", "watch:build": "npm run build -- --watch", "watch:lint": "chokidar 'src/**/*.js' 'test/**/*.js' -c 'eslint {path}'", "watch:test": "ava --watch", "watch": "run-s watch:**", "preversion": "npm test", "version": "npm run build && git add -A dist", "postversion": "git branch -f latest master", "push-release": "git push origin master latest && git push --tags", "prepublishOnly": "npm run build && git add -A dist"}, "main": "dist/player.js", "module": "dist/player.es.js", "jsnext:main": "dist/player.es.js", "directories": {"lib": "src/"}, "files": ["src", "dist"], "repository": "vimeo/player.js", "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "bugs": "https://github.com/vimeo/player.js/issues", "keywords": ["vimeo", "player", "api", "iframe", "embed", "video", "javascript", "postMessage"], "dependencies": {"native-promise-only": "0.8.1", "weakmap-polyfill": "2.0.4"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/eslint-parser": "^7.21.8", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-syntax-export-default-from": "^7.18.6", "@babel/plugin-transform-classes": "^7.21.0", "@babel/plugin-transform-object-assign": "^7.18.6", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/preset-env": "^7.21.5", "@babel/register": "^7.21.0", "ava": "5.3.1", "chalk": "4.1.2", "chokidar": "^4.0.3", "chokidar-cli": "^3.0.0", "cross-env": "^7.0.3", "eslint": "7.32.0", "eslint-plugin-ava": "13.2.0", "eslint-plugin-compat": "^6.0.2", "jquery": "^3.7.1", "jsdom": "^26.1.0", "maxmin": "3.0.0", "npm-run-all": "^4.1.5", "nyc": "^17.1.0", "rimraf": "5.0.10", "rollup": "2.39.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "sinon": "^21.0.0", "timing-object": "^3.1.92", "uglify-js": "^3.19.3", "xhr2": "0.2.1"}, "babel": {"presets": [["@babel/preset-env", {"modules": false, "shippedProposals": true}]], "env": {"test": {"presets": ["@babel/preset-env"], "plugins": ["@babel/transform-runtime"], "sourceMaps": "inline"}}}, "ava": {"require": ["@babel/register", "./test/helpers/browser-env.js"], "ignoredByWatcher": ["!dist/**/*"]}, "nyc": {"require": ["@babel/register"], "exclude": ["node_modules", "src/lib/compatibility-check.js", "test"]}, "browserslist": ["> 1%", "last 2 versions", "Firefox ESR", "not ie < 11"], "volta": {"node": "18.20.8", "yarn": "1.22.22"}}