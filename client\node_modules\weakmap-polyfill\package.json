{"name": "weakmap-polyfill", "description": "ECMAScript6 WeakMap polyfill", "version": "2.0.4", "author": "polygonplanet <<EMAIL>>", "bugs": {"url": "https://github.com/polygonplanet/weakmap-polyfill/issues"}, "dependencies": {}, "devDependencies": {"es5-shim": "^4.5.15", "eslint": "^7.28.0", "karma": "^6.3.3", "karma-chrome-launcher": "^3.1.0", "karma-detect-browsers": "^2.3.3", "karma-firefox-launcher": "^2.1.1", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-safari-launcher": "^1.0.0", "mocha": "^8.4.0", "power-assert": "^1.6.1", "uglify-js": "^3.13.9"}, "engines": {"node": ">=8.10.0"}, "files": ["weakmap-polyfill.js", "weakmap-polyfill.min.js"], "homepage": "https://github.com/polygonplanet/weakmap-polyfill", "keywords": ["ecmascript", "es2015", "es6", "javascript", "polyfill", "shim", "weakmap"], "license": "MIT", "main": "weakmap-polyfill.js", "repository": {"type": "git", "url": "https://github.com/polygonplanet/weakmap-polyfill.git"}, "scripts": {"minify": "uglifyjs weakmap-polyfill.js -o weakmap-polyfill.min.js --keep-fnames --comments -m -b ascii_only=true,beautify=false", "test": "mocha tests/test-node && mocha tests/test-node-polyfill && karma start karma.conf.js", "travis": "mocha tests/test-node && mocha tests/test-node-polyfill && karma start karma.conf.js --single-run"}}