{"version": 3, "file": "player.js", "sources": ["src/lib/functions.js", "src/lib/compatibility-check.js", "node_modules/weakmap-polyfill/weakmap-polyfill.js", "node_modules/native-promise-only/lib/npo.src.js", "src/lib/callbacks.js", "src/lib/postmessage.js", "src/lib/embed.js", "src/lib/screenfull.js", "src/lib/timing-src-connector.js", "src/player.js"], "sourcesContent": ["/**\n * @module lib/functions\n */\n\n/**\n * Check to see this is a Node environment.\n * @type {boolean}\n */\n/* global global */\nexport const isNode = typeof global !== 'undefined' &&\n  ({}).toString.call(global) === '[object global]';\n\n/**\n * Check to see if this is a Bun environment.\n * @see https://bun.sh/guides/util/detect-bun\n * @type {boolean}\n */\nexport const isBun = typeof Bun !== 'undefined';\n\n/**\n * Check to see if this is a Deno environment.\n * @see https://docs.deno.com/api/deno/~/Deno\n * @type {boolean}\n */\nexport const isDeno = typeof Deno !== 'undefined';\n\n/**\n * Check if this is a server runtime\n * @type {boolean}\n */\nexport const isServerRuntime = isNode || isBun || isDeno;\n\n/**\n * Get the name of the method for a given getter or setter.\n *\n * @param {string} prop The name of the property.\n * @param {string} type Either “get” or “set”.\n * @return {string}\n */\nexport function getMethodName(prop, type) {\n    if (prop.indexOf(type.toLowerCase()) === 0) {\n        return prop;\n    }\n\n    return `${type.toLowerCase()}${prop.substr(0, 1).toUpperCase()}${prop.substr(1)}`;\n}\n\n/**\n * Check to see if the object is a DOM Element.\n *\n * @param {*} element The object to check.\n * @return {boolean}\n */\nexport function isDomElement(element) {\n    return Boolean(\n        element && element.nodeType === 1 && 'nodeName' in element &&\n        element.ownerDocument && element.ownerDocument.defaultView\n    );\n}\n\n/**\n * Check to see whether the value is a number.\n *\n * @see http://dl.dropboxusercontent.com/u/35146/js/tests/isNumber.html\n * @param {*} value The value to check.\n * @param {boolean} integer Check if the value is an integer.\n * @return {boolean}\n */\nexport function isInteger(value) {\n    // eslint-disable-next-line eqeqeq\n    return !isNaN(parseFloat(value)) && isFinite(value) && Math.floor(value) == value;\n}\n\n/**\n * Check to see if the URL is a Vimeo url.\n *\n * @param {string} url The url string.\n * @return {boolean}\n */\nexport function isVimeoUrl(url) {\n    return (/^(https?:)?\\/\\/((((player|www)\\.)?vimeo\\.com)|((player\\.)?[a-zA-Z0-9-]+\\.(videoji\\.(hk|cn)|vimeo\\.work)))(?=$|\\/)/).test(url);\n}\n\n/**\n * Check to see if the URL is for a Vimeo embed.\n *\n * @param {string} url The url string.\n * @return {boolean}\n */\nexport function isVimeoEmbed(url) {\n    const expr = /^https:\\/\\/player\\.((vimeo\\.com)|([a-zA-Z0-9-]+\\.(videoji\\.(hk|cn)|vimeo\\.work)))\\/video\\/\\d+/;\n    return expr.test(url);\n}\n\nexport function getOembedDomain(url) {\n    const match = (url || '').match(/^(?:https?:)?(?:\\/\\/)?([^/?]+)/);\n    const domain = ((match && match[1]) || '').replace('player.', '');\n    const customDomains = [\n        '.videoji.hk',\n        '.vimeo.work',\n        '.videoji.cn'\n    ];\n\n    for (const customDomain of customDomains) {\n        if (domain.endsWith(customDomain)) {\n            return domain;\n        }\n    }\n\n    return 'vimeo.com';\n}\n\n/**\n * Get the Vimeo URL from an element.\n * The element must have either a data-vimeo-id or data-vimeo-url attribute.\n *\n * @param {object} oEmbedParameters The oEmbed parameters.\n * @return {string}\n */\nexport function getVimeoUrl(oEmbedParameters = {}) {\n    const id = oEmbedParameters.id;\n    const url = oEmbedParameters.url;\n    const idOrUrl = id || url;\n\n    if (!idOrUrl) {\n        throw new Error('An id or url must be passed, either in an options object or as a data-vimeo-id or data-vimeo-url attribute.');\n    }\n\n    if (isInteger(idOrUrl)) {\n        return `https://vimeo.com/${idOrUrl}`;\n    }\n\n    if (isVimeoUrl(idOrUrl)) {\n        return idOrUrl.replace('http:', 'https:');\n    }\n\n    if (id) {\n        throw new TypeError(`“${id}” is not a valid video id.`);\n    }\n\n    throw new TypeError(`“${idOrUrl}” is not a vimeo.com url.`);\n}\n\n/* eslint-disable max-params */\n/**\n * A utility method for attaching and detaching event handlers\n *\n * @param {EventTarget} target\n * @param {string | string[]} eventName\n * @param {function} callback\n * @param {'addEventListener' | 'on'} onName\n * @param {'removeEventListener' | 'off'} offName\n * @return {{cancel: (function(): void)}}\n */\nexport const subscribe = (target, eventName, callback, onName = 'addEventListener', offName = 'removeEventListener') => {\n    const eventNames = typeof eventName === 'string' ? [eventName] : eventName;\n\n    eventNames.forEach((evName) => {\n        target[onName](evName, callback);\n    });\n\n    return {\n        cancel: () => eventNames.forEach((evName) => target[offName](evName, callback))\n    };\n};\n\nexport const logSurveyLink = () => {\n    console.log(\n        '\\n%cVimeo is looking for feedback!\\n%cComplete our survey about the Player SDK: https://t.maze.co/393567477',\n        'color:#00adef;font-size:1.2em;',\n        'color:#aaa;font-size:0.8em;'\n    );\n};\n\n/**\n * Find the iframe element that contains a specific source window\n *\n * @param {Window} sourceWindow The source window to find the iframe for\n * @param {Document} [doc=document] The document to search within\n * @return {HTMLIFrameElement|null} The iframe element if found, otherwise null\n */\nexport function findIframeBySourceWindow(sourceWindow, doc = document) {\n    if (!sourceWindow || !doc || typeof doc.querySelectorAll !== 'function') {\n        return null;\n    }\n\n    const iframes = doc.querySelectorAll('iframe');\n\n    for (let i = 0; i < iframes.length; i++) {\n        if (iframes[i] && iframes[i].contentWindow === sourceWindow) {\n            return iframes[i];\n        }\n    }\n\n    return null;\n}\n", "import { isServerRuntime } from './functions';\n\nconst arrayIndexOfSupport = typeof Array.prototype.indexOf !== 'undefined';\nconst postMessageSupport = typeof window !== 'undefined' && typeof window.postMessage !== 'undefined';\n\nif (!isServerRuntime && (!arrayIndexOfSupport || !postMessageSupport)) {\n    throw new Error('Sorry, the Vimeo Player API is not available in this browser.');\n}\n", "/*!\n * weakmap-polyfill v2.0.4 - ECMAScript6 WeakMap polyfill\n * https://github.com/polygonplanet/weakmap-polyfill\n * Copyright (c) 2015-2021 polygonplanet <<EMAIL>>\n * @license MIT\n */\n\n(function(self) {\n  'use strict';\n\n  if (self.WeakMap) {\n    return;\n  }\n\n  var hasOwnProperty = Object.prototype.hasOwnProperty;\n  var hasDefine = Object.defineProperty && (function() {\n    try {\n      // Avoid IE8's broken Object.defineProperty\n      return Object.defineProperty({}, 'x', { value: 1 }).x === 1;\n    } catch (e) {}\n  })();\n\n  var defineProperty = function(object, name, value) {\n    if (hasDefine) {\n      Object.defineProperty(object, name, {\n        configurable: true,\n        writable: true,\n        value: value\n      });\n    } else {\n      object[name] = value;\n    }\n  };\n\n  self.WeakMap = (function() {\n\n    // ECMA-262 23.3 WeakMap Objects\n    function WeakMap() {\n      if (this === void 0) {\n        throw new TypeError(\"Constructor WeakMap requires 'new'\");\n      }\n\n      defineProperty(this, '_id', genId('_WeakMap'));\n\n      // ECMA-262 23.3.1.1 WeakMap([iterable])\n      if (arguments.length > 0) {\n        // Currently, WeakMap `iterable` argument is not supported\n        throw new TypeError('WeakMap iterable is not supported');\n      }\n    }\n\n    // ECMA-262 23.3.3.2 WeakMap.prototype.delete(key)\n    defineProperty(WeakMap.prototype, 'delete', function(key) {\n      checkInstance(this, 'delete');\n\n      if (!isObject(key)) {\n        return false;\n      }\n\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        delete key[this._id];\n        return true;\n      }\n\n      return false;\n    });\n\n    // ECMA-262 23.3.3.3 WeakMap.prototype.get(key)\n    defineProperty(WeakMap.prototype, 'get', function(key) {\n      checkInstance(this, 'get');\n\n      if (!isObject(key)) {\n        return void 0;\n      }\n\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        return entry[1];\n      }\n\n      return void 0;\n    });\n\n    // ECMA-262 23.3.3.4 WeakMap.prototype.has(key)\n    defineProperty(WeakMap.prototype, 'has', function(key) {\n      checkInstance(this, 'has');\n\n      if (!isObject(key)) {\n        return false;\n      }\n\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        return true;\n      }\n\n      return false;\n    });\n\n    // ECMA-262 23.3.3.5 WeakMap.prototype.set(key, value)\n    defineProperty(WeakMap.prototype, 'set', function(key, value) {\n      checkInstance(this, 'set');\n\n      if (!isObject(key)) {\n        throw new TypeError('Invalid value used as weak map key');\n      }\n\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        entry[1] = value;\n        return this;\n      }\n\n      defineProperty(key, this._id, [key, value]);\n      return this;\n    });\n\n    function checkInstance(x, methodName) {\n      if (!isObject(x) || !hasOwnProperty.call(x, '_id')) {\n        throw new TypeError(\n          methodName + ' method called on incompatible receiver ' +\n          typeof x\n        );\n      }\n    }\n\n    function genId(prefix) {\n      return prefix + '_' + rand() + '.' + rand();\n    }\n\n    function rand() {\n      return Math.random().toString().substring(2);\n    }\n\n    defineProperty(WeakMap, '_polyfill', true);\n    return WeakMap;\n  })();\n\n  function isObject(x) {\n    return Object(x) === x;\n  }\n\n})(\n  typeof globalThis !== 'undefined' ? globalThis :\n  typeof self !== 'undefined' ? self :\n  typeof window !== 'undefined' ? window :\n  typeof global !== 'undefined' ? global : this\n);\n", "/*! Native Promise Only\n    v0.8.1 (c) <PERSON>\n    MIT License: http://getify.mit-license.org\n*/\n\n(function UMD(name,context,definition){\n\t// special form of UMD for polyfilling across evironments\n\tcontext[name] = context[name] || definition();\n\tif (typeof module != \"undefined\" && module.exports) { module.exports = context[name]; }\n\telse if (typeof define == \"function\" && define.amd) { define(function $AMD$(){ return context[name]; }); }\n})(\"Promise\",typeof global != \"undefined\" ? global : this,function DEF(){\n\t/*jshint validthis:true */\n\t\"use strict\";\n\n\tvar builtInProp, cycle, scheduling_queue,\n\t\tToString = Object.prototype.toString,\n\t\ttimer = (typeof setImmediate != \"undefined\") ?\n\t\t\tfunction timer(fn) { return setImmediate(fn); } :\n\t\t\tsetTimeout\n\t;\n\n\t// dammit, IE8.\n\ttry {\n\t\tObject.defineProperty({},\"x\",{});\n\t\tbuiltInProp = function builtInProp(obj,name,val,config) {\n\t\t\treturn Object.defineProperty(obj,name,{\n\t\t\t\tvalue: val,\n\t\t\t\twritable: true,\n\t\t\t\tconfigurable: config !== false\n\t\t\t});\n\t\t};\n\t}\n\tcatch (err) {\n\t\tbuiltInProp = function builtInProp(obj,name,val) {\n\t\t\tobj[name] = val;\n\t\t\treturn obj;\n\t\t};\n\t}\n\n\t// Note: using a queue instead of array for efficiency\n\tscheduling_queue = (function Queue() {\n\t\tvar first, last, item;\n\n\t\tfunction Item(fn,self) {\n\t\t\tthis.fn = fn;\n\t\t\tthis.self = self;\n\t\t\tthis.next = void 0;\n\t\t}\n\n\t\treturn {\n\t\t\tadd: function add(fn,self) {\n\t\t\t\titem = new Item(fn,self);\n\t\t\t\tif (last) {\n\t\t\t\t\tlast.next = item;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tfirst = item;\n\t\t\t\t}\n\t\t\t\tlast = item;\n\t\t\t\titem = void 0;\n\t\t\t},\n\t\t\tdrain: function drain() {\n\t\t\t\tvar f = first;\n\t\t\t\tfirst = last = cycle = void 0;\n\n\t\t\t\twhile (f) {\n\t\t\t\t\tf.fn.call(f.self);\n\t\t\t\t\tf = f.next;\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t})();\n\n\tfunction schedule(fn,self) {\n\t\tscheduling_queue.add(fn,self);\n\t\tif (!cycle) {\n\t\t\tcycle = timer(scheduling_queue.drain);\n\t\t}\n\t}\n\n\t// promise duck typing\n\tfunction isThenable(o) {\n\t\tvar _then, o_type = typeof o;\n\n\t\tif (o != null &&\n\t\t\t(\n\t\t\t\to_type == \"object\" || o_type == \"function\"\n\t\t\t)\n\t\t) {\n\t\t\t_then = o.then;\n\t\t}\n\t\treturn typeof _then == \"function\" ? _then : false;\n\t}\n\n\tfunction notify() {\n\t\tfor (var i=0; i<this.chain.length; i++) {\n\t\t\tnotifyIsolated(\n\t\t\t\tthis,\n\t\t\t\t(this.state === 1) ? this.chain[i].success : this.chain[i].failure,\n\t\t\t\tthis.chain[i]\n\t\t\t);\n\t\t}\n\t\tthis.chain.length = 0;\n\t}\n\n\t// NOTE: This is a separate function to isolate\n\t// the `try..catch` so that other code can be\n\t// optimized better\n\tfunction notifyIsolated(self,cb,chain) {\n\t\tvar ret, _then;\n\t\ttry {\n\t\t\tif (cb === false) {\n\t\t\t\tchain.reject(self.msg);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif (cb === true) {\n\t\t\t\t\tret = self.msg;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tret = cb.call(void 0,self.msg);\n\t\t\t\t}\n\n\t\t\t\tif (ret === chain.promise) {\n\t\t\t\t\tchain.reject(TypeError(\"Promise-chain cycle\"));\n\t\t\t\t}\n\t\t\t\telse if (_then = isThenable(ret)) {\n\t\t\t\t\t_then.call(ret,chain.resolve,chain.reject);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tchain.resolve(ret);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tcatch (err) {\n\t\t\tchain.reject(err);\n\t\t}\n\t}\n\n\tfunction resolve(msg) {\n\t\tvar _then, self = this;\n\n\t\t// already triggered?\n\t\tif (self.triggered) { return; }\n\n\t\tself.triggered = true;\n\n\t\t// unwrap\n\t\tif (self.def) {\n\t\t\tself = self.def;\n\t\t}\n\n\t\ttry {\n\t\t\tif (_then = isThenable(msg)) {\n\t\t\t\tschedule(function(){\n\t\t\t\t\tvar def_wrapper = new MakeDefWrapper(self);\n\t\t\t\t\ttry {\n\t\t\t\t\t\t_then.call(msg,\n\t\t\t\t\t\t\tfunction $resolve$(){ resolve.apply(def_wrapper,arguments); },\n\t\t\t\t\t\t\tfunction $reject$(){ reject.apply(def_wrapper,arguments); }\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\tcatch (err) {\n\t\t\t\t\t\treject.call(def_wrapper,err);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t\telse {\n\t\t\t\tself.msg = msg;\n\t\t\t\tself.state = 1;\n\t\t\t\tif (self.chain.length > 0) {\n\t\t\t\t\tschedule(notify,self);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tcatch (err) {\n\t\t\treject.call(new MakeDefWrapper(self),err);\n\t\t}\n\t}\n\n\tfunction reject(msg) {\n\t\tvar self = this;\n\n\t\t// already triggered?\n\t\tif (self.triggered) { return; }\n\n\t\tself.triggered = true;\n\n\t\t// unwrap\n\t\tif (self.def) {\n\t\t\tself = self.def;\n\t\t}\n\n\t\tself.msg = msg;\n\t\tself.state = 2;\n\t\tif (self.chain.length > 0) {\n\t\t\tschedule(notify,self);\n\t\t}\n\t}\n\n\tfunction iteratePromises(Constructor,arr,resolver,rejecter) {\n\t\tfor (var idx=0; idx<arr.length; idx++) {\n\t\t\t(function IIFE(idx){\n\t\t\t\tConstructor.resolve(arr[idx])\n\t\t\t\t.then(\n\t\t\t\t\tfunction $resolver$(msg){\n\t\t\t\t\t\tresolver(idx,msg);\n\t\t\t\t\t},\n\t\t\t\t\trejecter\n\t\t\t\t);\n\t\t\t})(idx);\n\t\t}\n\t}\n\n\tfunction MakeDefWrapper(self) {\n\t\tthis.def = self;\n\t\tthis.triggered = false;\n\t}\n\n\tfunction MakeDef(self) {\n\t\tthis.promise = self;\n\t\tthis.state = 0;\n\t\tthis.triggered = false;\n\t\tthis.chain = [];\n\t\tthis.msg = void 0;\n\t}\n\n\tfunction Promise(executor) {\n\t\tif (typeof executor != \"function\") {\n\t\t\tthrow TypeError(\"Not a function\");\n\t\t}\n\n\t\tif (this.__NPO__ !== 0) {\n\t\t\tthrow TypeError(\"Not a promise\");\n\t\t}\n\n\t\t// instance shadowing the inherited \"brand\"\n\t\t// to signal an already \"initialized\" promise\n\t\tthis.__NPO__ = 1;\n\n\t\tvar def = new MakeDef(this);\n\n\t\tthis[\"then\"] = function then(success,failure) {\n\t\t\tvar o = {\n\t\t\t\tsuccess: typeof success == \"function\" ? success : true,\n\t\t\t\tfailure: typeof failure == \"function\" ? failure : false\n\t\t\t};\n\t\t\t// Note: `then(..)` itself can be borrowed to be used against\n\t\t\t// a different promise constructor for making the chained promise,\n\t\t\t// by substituting a different `this` binding.\n\t\t\to.promise = new this.constructor(function extractChain(resolve,reject) {\n\t\t\t\tif (typeof resolve != \"function\" || typeof reject != \"function\") {\n\t\t\t\t\tthrow TypeError(\"Not a function\");\n\t\t\t\t}\n\n\t\t\t\to.resolve = resolve;\n\t\t\t\to.reject = reject;\n\t\t\t});\n\t\t\tdef.chain.push(o);\n\n\t\t\tif (def.state !== 0) {\n\t\t\t\tschedule(notify,def);\n\t\t\t}\n\n\t\t\treturn o.promise;\n\t\t};\n\t\tthis[\"catch\"] = function $catch$(failure) {\n\t\t\treturn this.then(void 0,failure);\n\t\t};\n\n\t\ttry {\n\t\t\texecutor.call(\n\t\t\t\tvoid 0,\n\t\t\t\tfunction publicResolve(msg){\n\t\t\t\t\tresolve.call(def,msg);\n\t\t\t\t},\n\t\t\t\tfunction publicReject(msg) {\n\t\t\t\t\treject.call(def,msg);\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\t\tcatch (err) {\n\t\t\treject.call(def,err);\n\t\t}\n\t}\n\n\tvar PromisePrototype = builtInProp({},\"constructor\",Promise,\n\t\t/*configurable=*/false\n\t);\n\n\t// Note: Android 4 cannot use `Object.defineProperty(..)` here\n\tPromise.prototype = PromisePrototype;\n\n\t// built-in \"brand\" to signal an \"uninitialized\" promise\n\tbuiltInProp(PromisePrototype,\"__NPO__\",0,\n\t\t/*configurable=*/false\n\t);\n\n\tbuiltInProp(Promise,\"resolve\",function Promise$resolve(msg) {\n\t\tvar Constructor = this;\n\n\t\t// spec mandated checks\n\t\t// note: best \"isPromise\" check that's practical for now\n\t\tif (msg && typeof msg == \"object\" && msg.__NPO__ === 1) {\n\t\t\treturn msg;\n\t\t}\n\n\t\treturn new Constructor(function executor(resolve,reject){\n\t\t\tif (typeof resolve != \"function\" || typeof reject != \"function\") {\n\t\t\t\tthrow TypeError(\"Not a function\");\n\t\t\t}\n\n\t\t\tresolve(msg);\n\t\t});\n\t});\n\n\tbuiltInProp(Promise,\"reject\",function Promise$reject(msg) {\n\t\treturn new this(function executor(resolve,reject){\n\t\t\tif (typeof resolve != \"function\" || typeof reject != \"function\") {\n\t\t\t\tthrow TypeError(\"Not a function\");\n\t\t\t}\n\n\t\t\treject(msg);\n\t\t});\n\t});\n\n\tbuiltInProp(Promise,\"all\",function Promise$all(arr) {\n\t\tvar Constructor = this;\n\n\t\t// spec mandated checks\n\t\tif (ToString.call(arr) != \"[object Array]\") {\n\t\t\treturn Constructor.reject(TypeError(\"Not an array\"));\n\t\t}\n\t\tif (arr.length === 0) {\n\t\t\treturn Constructor.resolve([]);\n\t\t}\n\n\t\treturn new Constructor(function executor(resolve,reject){\n\t\t\tif (typeof resolve != \"function\" || typeof reject != \"function\") {\n\t\t\t\tthrow TypeError(\"Not a function\");\n\t\t\t}\n\n\t\t\tvar len = arr.length, msgs = Array(len), count = 0;\n\n\t\t\titeratePromises(Constructor,arr,function resolver(idx,msg) {\n\t\t\t\tmsgs[idx] = msg;\n\t\t\t\tif (++count === len) {\n\t\t\t\t\tresolve(msgs);\n\t\t\t\t}\n\t\t\t},reject);\n\t\t});\n\t});\n\n\tbuiltInProp(Promise,\"race\",function Promise$race(arr) {\n\t\tvar Constructor = this;\n\n\t\t// spec mandated checks\n\t\tif (ToString.call(arr) != \"[object Array]\") {\n\t\t\treturn Constructor.reject(TypeError(\"Not an array\"));\n\t\t}\n\n\t\treturn new Constructor(function executor(resolve,reject){\n\t\t\tif (typeof resolve != \"function\" || typeof reject != \"function\") {\n\t\t\t\tthrow TypeError(\"Not a function\");\n\t\t\t}\n\n\t\t\titeratePromises(Constructor,arr,function resolver(idx,msg){\n\t\t\t\tresolve(msg);\n\t\t\t},reject);\n\t\t});\n\t});\n\n\treturn Promise;\n});\n", "/**\n * @module lib/callbacks\n */\n\nexport const callbackMap = new WeakMap();\n\n/**\n * Store a callback for a method or event for a player.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name.\n * @param {(function(this:Player, *): void|{resolve: function, reject: function})} callback\n *        The callback to call or an object with resolve and reject functions for a promise.\n * @return {void}\n */\nexport function storeCallback(player, name, callback) {\n    const playerCallbacks = callbackMap.get(player.element) || {};\n\n    if (!(name in playerCallbacks)) {\n        playerCallbacks[name] = [];\n    }\n\n    playerCallbacks[name].push(callback);\n    callbackMap.set(player.element, playerCallbacks);\n}\n\n/**\n * Get the callbacks for a player and event or method.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name\n * @return {function[]}\n */\nexport function getCallbacks(player, name) {\n    const playerCallbacks = callbackMap.get(player.element) || {};\n    return playerCallbacks[name] || [];\n}\n\n/**\n * Remove a stored callback for a method or event for a player.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name\n * @param {function} [callback] The specific callback to remove.\n * @return {boolean} Was this the last callback?\n */\nexport function removeCallback(player, name, callback) {\n    const playerCallbacks = callbackMap.get(player.element) || {};\n\n    if (!playerCallbacks[name]) {\n        return true;\n    }\n\n    // If no callback is passed, remove all callbacks for the event\n    if (!callback) {\n        playerCallbacks[name] = [];\n        callbackMap.set(player.element, playerCallbacks);\n\n        return true;\n    }\n\n    const index = playerCallbacks[name].indexOf(callback);\n\n    if (index !== -1) {\n        playerCallbacks[name].splice(index, 1);\n    }\n\n    callbackMap.set(player.element, playerCallbacks);\n    return playerCallbacks[name] && playerCallbacks[name].length === 0;\n}\n\n/**\n * Return the first stored callback for a player and event or method.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name.\n * @return {function} The callback, or false if there were none\n */\nexport function shiftCallbacks(player, name) {\n    const playerCallbacks = getCallbacks(player, name);\n\n    if (playerCallbacks.length < 1) {\n        return false;\n    }\n\n    const callback = playerCallbacks.shift();\n    removeCallback(player, name, callback);\n    return callback;\n}\n\n/**\n * Move callbacks associated with an element to another element.\n *\n * @param {HTMLElement} oldElement The old element.\n * @param {HTMLElement} newElement The new element.\n * @return {void}\n */\nexport function swapCallbacks(oldElement, newElement) {\n    const playerCallbacks = callbackMap.get(oldElement);\n\n    callbackMap.set(newElement, playerCallbacks);\n    callbackMap.delete(oldElement);\n}\n", "/**\n * @module lib/postmessage\n */\n\nimport { getCallbacks, removeCallback, shiftCallbacks } from './callbacks';\n\n/**\n * Parse a message received from postMessage.\n *\n * @param {*} data The data received from postMessage.\n * @return {object}\n */\nexport function parseMessageData(data) {\n    if (typeof data === 'string') {\n        try {\n            data = JSON.parse(data);\n        }\n        catch (error) {\n            // If the message cannot be parsed, throw the error as a warning\n            console.warn(error);\n            return {};\n        }\n    }\n\n    return data;\n}\n\n/**\n * Post a message to the specified target.\n *\n * @param {Player} player The player object to use.\n * @param {string} method The API method to call.\n * @param {string|number|object|Array|undefined} params The parameters to send to the player.\n * @return {void}\n */\nexport function postMessage(player, method, params) {\n    if (!player.element.contentWindow || !player.element.contentWindow.postMessage) {\n        return;\n    }\n\n    let message = {\n        method\n    };\n\n    if (params !== undefined) {\n        message.value = params;\n    }\n\n    // IE 8 and 9 do not support passing messages, so stringify them\n    const ieVersion = parseFloat(navigator.userAgent.toLowerCase().replace(/^.*msie (\\d+).*$/, '$1'));\n    if (ieVersion >= 8 && ieVersion < 10) {\n        message = JSON.stringify(message);\n    }\n\n    player.element.contentWindow.postMessage(message, player.origin);\n}\n\n/**\n * Parse the data received from a message event.\n *\n * @param {Player} player The player that received the message.\n * @param {(Object|string)} data The message data. Strings will be parsed into JSON.\n * @return {void}\n */\nexport function processData(player, data) {\n    data = parseMessageData(data);\n    let callbacks = [];\n    let param;\n\n    if (data.event) {\n        if (data.event === 'error') {\n            const promises = getCallbacks(player, data.data.method);\n\n            promises.forEach((promise) => {\n                const error = new Error(data.data.message);\n                error.name = data.data.name;\n\n                promise.reject(error);\n                removeCallback(player, data.data.method, promise);\n            });\n        }\n\n        callbacks = getCallbacks(player, `event:${data.event}`);\n        param = data.data;\n    }\n    else if (data.method) {\n        const callback = shiftCallbacks(player, data.method);\n\n        if (callback) {\n            callbacks.push(callback);\n            param = data.value;\n        }\n    }\n\n    callbacks.forEach((callback) => {\n        try {\n            if (typeof callback === 'function') {\n                callback.call(player, param);\n                return;\n            }\n\n            callback.resolve(param);\n        }\n        catch (e) {\n            // empty\n        }\n    });\n}\n", "/**\n * @module lib/embed\n */\n\nimport Player from '../player';\nimport { isVimeoUrl, getVimeoUrl, getOembedDomain, isVimeoEmbed, findIframeBySourceWindow } from './functions';\nimport { parseMessageData } from './postmessage';\n\nconst oEmbedParameters = [\n    'airplay',\n    'audio_tracks',\n    'audiotrack',\n    'autopause',\n    'autoplay',\n    'background',\n    'byline',\n    'cc',\n    'chapter_id',\n    'chapters',\n    'chromecast',\n    'color',\n    'colors',\n    'controls',\n    'dnt',\n    'end_time',\n    'fullscreen',\n    'height',\n    'id',\n    'initial_quality',\n    'interactive_params',\n    'keyboard',\n    'loop',\n    'maxheight',\n    'max_quality',\n    'maxwidth',\n    'min_quality',\n    'muted',\n    'play_button_position',\n    'playsinline',\n    'portrait',\n    'preload',\n    'progress_bar',\n    'quality',\n    'quality_selector',\n    'responsive',\n    'skipping_forward',\n    'speed',\n    'start_time',\n    'texttrack',\n    'thumbnail_id',\n    'title',\n    'transcript',\n    'transparent',\n    'unmute_button',\n    'url',\n    'vimeo_logo',\n    'volume',\n    'watch_full_video',\n    'width'\n];\n\n/**\n * Get the 'data-vimeo'-prefixed attributes from an element as an object.\n *\n * @param {HTMLElement} element The element.\n * @param {Object} [defaults={}] The default values to use.\n * @return {Object<string, string>}\n */\nexport function getOEmbedParameters(element, defaults = {}) {\n    return oEmbedParameters.reduce((params, param) => {\n        const value = element.getAttribute(`data-vimeo-${param}`);\n\n        if (value || value === '') {\n            params[param] = value === '' ? 1 : value;\n        }\n\n        return params;\n    }, defaults);\n}\n\n/**\n * Create an embed from oEmbed data inside an element.\n *\n * @param {object} data The oEmbed data.\n * @param {HTMLElement} element The element to put the iframe in.\n * @return {HTMLIFrameElement} The iframe embed.\n */\nexport function createEmbed({ html }, element) {\n    if (!element) {\n        throw new TypeError('An element must be provided');\n    }\n\n    if (element.getAttribute('data-vimeo-initialized') !== null) {\n        return element.querySelector('iframe');\n    }\n\n    const div = document.createElement('div');\n    div.innerHTML = html;\n\n    element.appendChild(div.firstChild);\n    element.setAttribute('data-vimeo-initialized', 'true');\n\n    return element.querySelector('iframe');\n}\n\n/**\n * Make an oEmbed call for the specified URL.\n *\n * @param {string} videoUrl The vimeo.com url for the video.\n * @param {Object} [params] Parameters to pass to oEmbed.\n * @param {HTMLElement} element The element.\n * @return {Promise}\n */\nexport function getOEmbedData(videoUrl, params = {}, element) {\n    return new Promise((resolve, reject) => {\n        if (!isVimeoUrl(videoUrl)) {\n            throw new TypeError(`“${videoUrl}” is not a vimeo.com url.`);\n        }\n\n        const domain = getOembedDomain(videoUrl);\n        let url = `https://${domain}/api/oembed.json?url=${encodeURIComponent(videoUrl)}`;\n\n        for (const param in params) {\n            if (params.hasOwnProperty(param)) {\n                url += `&${param}=${encodeURIComponent(params[param])}`;\n            }\n        }\n\n        const xhr = 'XDomainRequest' in window ? new XDomainRequest() : new XMLHttpRequest();\n        xhr.open('GET', url, true);\n\n        xhr.onload = function() {\n            if (xhr.status === 404) {\n                reject(new Error(`“${videoUrl}” was not found.`));\n                return;\n            }\n\n            if (xhr.status === 403) {\n                reject(new Error(`“${videoUrl}” is not embeddable.`));\n                return;\n            }\n\n            try {\n                const json = JSON.parse(xhr.responseText);\n                // Check api response for 403 on oembed\n                if (json.domain_status_code === 403) {\n                    // We still want to create the embed to give users visual feedback\n                    createEmbed(json, element);\n                    reject(new Error(`“${videoUrl}” is not embeddable.`));\n                    return;\n                }\n\n                resolve(json);\n            }\n            catch (error) {\n                reject(error);\n            }\n        };\n\n        xhr.onerror = function() {\n            const status = xhr.status ? ` (${xhr.status})` : '';\n            reject(new Error(`There was an error fetching the embed code from Vimeo${status}.`));\n        };\n\n        xhr.send();\n    });\n}\n\n/**\n * Initialize all embeds within a specific element\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nexport function initializeEmbeds(parent = document) {\n    const elements = [].slice.call(parent.querySelectorAll('[data-vimeo-id], [data-vimeo-url]'));\n\n    const handleError = (error) => {\n        if ('console' in window && console.error) {\n            console.error(`There was an error creating an embed: ${error}`);\n        }\n    };\n\n    elements.forEach((element) => {\n        try {\n            // Skip any that have data-vimeo-defer\n            if (element.getAttribute('data-vimeo-defer') !== null) {\n                return;\n            }\n\n            const params = getOEmbedParameters(element);\n            const url = getVimeoUrl(params);\n\n            getOEmbedData(url, params, element).then((data) => {\n                return createEmbed(data, element);\n            }).catch(handleError);\n        }\n        catch (error) {\n            handleError(error);\n        }\n    });\n}\n\n/**\n * Resize embeds when messaged by the player.\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nexport function resizeEmbeds(parent = document) {\n    // Prevent execution if users include the player.js script multiple times.\n    if (window.VimeoPlayerResizeEmbeds_) {\n        return;\n    }\n    window.VimeoPlayerResizeEmbeds_ = true;\n\n    const onMessage = (event) => {\n        if (!isVimeoUrl(event.origin)) {\n            return;\n        }\n\n        // 'spacechange' is fired only on embeds with cards\n        if (!event.data || event.data.event !== 'spacechange') {\n            return;\n        }\n\n        const senderIFrame = event.source ? findIframeBySourceWindow(event.source, parent) : null;\n\n        if (senderIFrame) {\n            // Change padding-bottom of the enclosing div to accommodate\n            // card carousel without distorting aspect ratio\n            const space = senderIFrame.parentElement;\n            space.style.paddingBottom = `${event.data.data[0].bottom}px`;\n        }\n    };\n\n    window.addEventListener('message', onMessage);\n}\n\n/**\n * Add chapters to existing metadata for Google SEO\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nexport function initAppendVideoMetadata(parent = document) {\n    //  Prevent execution if users include the player.js script multiple times.\n    if (window.VimeoSeoMetadataAppended) {\n        return;\n    }\n    window.VimeoSeoMetadataAppended = true;\n\n    const onMessage = (event) => {\n        if (!isVimeoUrl(event.origin)) {\n            return;\n        }\n\n        const data = parseMessageData(event.data);\n        if (!data || data.event !== 'ready') {\n            return;\n        }\n\n        const senderIFrame = event.source ? findIframeBySourceWindow(event.source, parent) : null;\n\n        // Initiate appendVideoMetadata if iframe is a Vimeo embed\n        if (senderIFrame && isVimeoEmbed(senderIFrame.src)) {\n            const player = new Player(senderIFrame);\n            player.callMethod('appendVideoMetadata', window.location.href);\n        }\n    };\n\n    window.addEventListener('message', onMessage);\n}\n\n/**\n * Seek to time indicated by vimeo_t query parameter if present in URL\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nexport function checkUrlTimeParam(parent = document) {\n    //  Prevent execution if users include the player.js script multiple times.\n    if (window.VimeoCheckedUrlTimeParam) {\n        return;\n    }\n    window.VimeoCheckedUrlTimeParam = true;\n\n    const handleError = (error) => {\n        if ('console' in window && console.error) {\n            console.error(`There was an error getting video Id: ${error}`);\n        }\n    };\n\n    const onMessage = (event) => {\n        if (!isVimeoUrl(event.origin)) {\n            return;\n        }\n\n        const data = parseMessageData(event.data);\n        if (!data || data.event !== 'ready') {\n            return;\n        }\n\n        const senderIFrame = event.source ? findIframeBySourceWindow(event.source, parent) : null;\n\n        if (senderIFrame && isVimeoEmbed(senderIFrame.src)) {\n            const player = new Player(senderIFrame);\n            player\n                .getVideoId()\n                .then((videoId) => {\n                    const matches = new RegExp(`[?&]vimeo_t_${videoId}=([^&#]*)`).exec(window.location.href);\n                    if (matches && matches[1]) {\n                        const sec = decodeURI(matches[1]);\n                        player.setCurrentTime(sec);\n                    }\n                    return;\n                })\n                .catch(handleError);\n        }\n    };\n\n    window.addEventListener('message', onMessage);\n}\n\n\n/**\n * Updates iframe embeds to support DRM content playback by adding the 'encrypted-media' permission\n * to the iframe's allow attribute when DRM initialization fails. This function acts as a fallback\n * mechanism to enable playback of DRM-protected content in embeds that weren't properly configured.\n *\n * @return {void}\n */\nexport function updateDRMEmbeds() {\n    if (window.VimeoDRMEmbedsUpdated) {\n        return;\n    }\n    window.VimeoDRMEmbedsUpdated = true;\n\n    /**\n     * Handle message events for DRM initialization failures\n     * @param {MessageEvent} event - The message event from the iframe\n     */\n    const onMessage = (event) => {\n        if (!isVimeoUrl(event.origin)) {\n            return;\n        }\n\n        const data = parseMessageData(event.data);\n        if (!data || data.event !== 'drminitfailed') {\n            return;\n        }\n\n        const senderIFrame = event.source ? findIframeBySourceWindow(event.source) : null;\n\n        if (!senderIFrame) {\n            return;\n        }\n\n        const currentAllow = senderIFrame.getAttribute('allow') || '';\n        const allowSupportsDRM = currentAllow.includes('encrypted-media');\n\n        if (!allowSupportsDRM) {\n            // For DRM playback to successfully occur, the iframe `allow` attribute must include 'encrypted-media'.\n            // If the video requires DRM but doesn't have the attribute, we try to add on behalf of the embed owner\n            // as a temporary measure to enable playback until they're able to update their embeds.\n            senderIFrame.setAttribute('allow', `${currentAllow}; encrypted-media`);\n            const currentUrl = new URL(senderIFrame.getAttribute('src'));\n\n            // Adding this forces the embed to reload once `allow` has been updated with `encrypted-media`.\n            currentUrl.searchParams.set('forcereload', 'drm');\n            senderIFrame.setAttribute('src', currentUrl.toString());\n            return;\n        }\n    };\n\n    window.addEventListener('message', onMessage);\n}\n", "/* MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\nTerms */\n\nexport function initializeScreenfull() {\n\n    const fn = (function() {\n        let val;\n\n        const fnMap = [\n            [\n                'requestFullscreen',\n                'exitFullscreen',\n                'fullscreenElement',\n                'fullscreenEnabled',\n                'fullscreenchange',\n                'fullscreenerror'\n            ],\n            // New WebKit\n            [\n                'webkitRequestFullscreen',\n                'webkitExitFullscreen',\n                'webkitFullscreenElement',\n                'webkitFullscreenEnabled',\n                'webkitfullscreenchange',\n                'webkitfullscreenerror'\n\n            ],\n            // Old WebKit\n            [\n                'webkitRequestFullScreen',\n                'webkitCancelFullScreen',\n                'webkitCurrentFullScreenElement',\n                'webkitCancelFullScreen',\n                'webkitfullscreenchange',\n                'webkitfullscreenerror'\n\n            ],\n            [\n                'mozRequestFullScreen',\n                'mozCancelFullScreen',\n                'mozFullScreenElement',\n                'mozFullScreenEnabled',\n                'mozfullscreenchange',\n                'mozfullscreenerror'\n            ],\n            [\n                'msRequestFullscreen',\n                'msExitFullscreen',\n                'msFullscreenElement',\n                'msFullscreenEnabled',\n                'MSFullscreenChange',\n                'MSFullscreenError'\n            ]\n        ];\n\n        let i = 0;\n        const l = fnMap.length;\n        const ret = {};\n\n        for (; i < l; i++) {\n            val = fnMap[i];\n            if (val && val[1] in document) {\n                for (i = 0; i < val.length; i++) {\n                    ret[fnMap[0][i]] = val[i];\n                }\n                return ret;\n            }\n        }\n\n        return false;\n    }());\n\n    const eventNameMap = {\n        fullscreenchange: fn.fullscreenchange,\n        fullscreenerror: fn.fullscreenerror\n    };\n\n    const screenfull = {\n        request(element) {\n            return new Promise((resolve, reject) => {\n                const onFullScreenEntered = function() {\n                    screenfull.off('fullscreenchange', onFullScreenEntered);\n                    resolve();\n                };\n\n                screenfull.on('fullscreenchange', onFullScreenEntered);\n\n                element = element || document.documentElement;\n\n                const returnPromise = element[fn.requestFullscreen]();\n                if (returnPromise instanceof Promise) {\n                    returnPromise.then(onFullScreenEntered).catch(reject);\n                }\n            });\n        },\n        exit() {\n            return new Promise((resolve, reject) => {\n                if (!screenfull.isFullscreen) {\n                    resolve();\n                    return;\n                }\n\n                const onFullScreenExit = function() {\n                    screenfull.off('fullscreenchange', onFullScreenExit);\n                    resolve();\n                };\n\n                screenfull.on('fullscreenchange', onFullScreenExit);\n\n                const returnPromise = document[fn.exitFullscreen]();\n                if (returnPromise instanceof Promise) {\n                    returnPromise.then(onFullScreenExit).catch(reject);\n                }\n            });\n        },\n        on(event, callback) {\n            const eventName = eventNameMap[event];\n            if (eventName) {\n                document.addEventListener(eventName, callback);\n            }\n        },\n        off(event, callback) {\n            const eventName = eventNameMap[event];\n            if (eventName) {\n                document.removeEventListener(eventName, callback);\n            }\n        }\n    };\n\n    Object.defineProperties(screenfull, {\n        isFullscreen: {\n            get() {\n                return Boolean(document[fn.fullscreenElement]);\n            }\n        },\n        element: {\n            enumerable: true,\n            get() {\n                return document[fn.fullscreenElement];\n            }\n        },\n        isEnabled: {\n            enumerable: true,\n            get() {\n                // Coerce to boolean in case of old WebKit\n                return Boolean(document[fn.fullscreenEnabled]);\n            }\n        }\n    });\n\n    return screenfull;\n}\n", "import { subscribe } from './functions';\n\n/** @typedef {import('./timing-src-connector.types').PlayerControls} PlayerControls */\n/** @typedef {import('timing-object').ITimingObject} TimingObject */\n/** @typedef {import('./timing-src-connector.types').TimingSrcConnectorOptions} TimingSrcConnectorOptions */\n/** @typedef {(msg: string) => any} Logger */\n/** @typedef {import('timing-object').TConnectionState} TConnectionState */\n\n/**\n * @type {TimingSrcConnectorOptions}\n *\n * For details on these properties and their effects, see the typescript definition referenced above.\n */\nconst defaultOptions = {\n    role: 'viewer',\n    autoPlayMuted: true,\n    allowedDrift: 0.3,\n    maxAllowedDrift: 1,\n    minCheckInterval: 0.1,\n    maxRateAdjustment: 0.2,\n    maxTimeToCatchUp: 1\n};\n\n\n/**\n * There's a proposed W3C spec for the Timing Object which would introduce a new set of APIs that would simplify time-synchronization tasks for browser applications.\n *\n * Proposed spec: https://webtiming.github.io/timingobject/\n * V3 Spec: https://timingsrc.readthedocs.io/en/latest/\n * Demuxed talk: https://www.youtube.com/watch?v=cZSjDaGDmX8\n *\n * This class makes it easy to connect Vimeo.Player to a provided TimingObject via Vimeo.Player.setTimingSrc(myTimingObject, options) and the synchronization will be handled automatically.\n *\n * There are 5 general responsibilities in TimingSrcConnector:\n *\n * 1. `updatePlayer()` which sets the player's currentTime, playbackRate and pause/play state based on current state of the TimingObject.\n * 2. `updateTimingObject()` which sets the TimingObject's position and velocity from the player's state.\n * 3. `playerUpdater` which listens for change events on the TimingObject and will respond by calling updatePlayer.\n * 4. `timingObjectUpdater` which listens to the player events of seeked, play and pause and will respond by calling `updateTimingObject()`.\n * 5. `maintainPlaybackPosition` this is code that constantly monitors the player to make sure it's always in sync with the TimingObject. This is needed because videos will generally not play with precise time accuracy and there will be some drift which becomes more noticeable over longer periods (as noted in the timing-object spec). More details on this method below.\n */\nexport class TimingSrcConnector extends EventTarget {\n    logger;\n\n    /**\n     * @param {PlayerControls} player\n     * @param {TimingObject} timingObject\n     * @param {TimingSrcConnectorOptions} options\n     * @param {Logger} logger\n     */\n    constructor(player, timingObject, options = {}, logger) {\n        super();\n        this.logger = logger;\n        this.init(timingObject, player, { ...defaultOptions, ...options });\n    }\n\n    disconnect() {\n        this.dispatchEvent(new Event('disconnect'));\n    }\n\n\n    /**\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {Promise<void>}\n     */\n    async init(timingObject, player, options) {\n        await this.waitForTOReadyState(timingObject, 'open');\n\n        if (options.role === 'viewer') {\n            await this.updatePlayer(timingObject, player, options);\n            const playerUpdater = subscribe(timingObject, 'change', () => this.updatePlayer(timingObject, player, options));\n            const positionSync = this.maintainPlaybackPosition(timingObject, player, options);\n            this.addEventListener('disconnect', () => {\n                positionSync.cancel();\n                playerUpdater.cancel();\n            });\n        }\n        else {\n            await this.updateTimingObject(timingObject, player);\n            const timingObjectUpdater = subscribe(player, ['seeked', 'play', 'pause', 'ratechange'], () => this.updateTimingObject(timingObject, player), 'on', 'off');\n            this.addEventListener('disconnect', () => timingObjectUpdater.cancel());\n        }\n    }\n\n    /**\n     * Sets the TimingObject's state to reflect that of the player\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @return {Promise<void>}\n     */\n    async updateTimingObject(timingObject, player) {\n        const [\n            position,\n            isPaused,\n            playbackRate\n        ] = await Promise.all([player.getCurrentTime(), player.getPaused(), player.getPlaybackRate()]);\n\n        timingObject.update({\n            position,\n            velocity: isPaused ? 0 : playbackRate\n        });\n    }\n\n\n    /**\n     * Sets the player's timing state to reflect that of the TimingObject\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {Promise<void>}\n     */\n    async updatePlayer(timingObject, player, options) {\n        const { position, velocity } = timingObject.query();\n        if (typeof position === 'number') {\n            player.setCurrentTime(position);\n        }\n        if (typeof velocity === 'number') {\n            if (velocity === 0) {\n                if ((await player.getPaused()) === false) {\n                    player.pause();\n                }\n            }\n            else if (velocity > 0) {\n                if ((await player.getPaused()) === true) {\n                    await player.play()\n                        .catch(async(err) => {\n                            if (err.name === 'NotAllowedError' && options.autoPlayMuted) {\n                                await player.setMuted(true);\n                                await player.play().catch((err2) => player.allowLogging && console.error('Couldn\\'t play the video from TimingSrcConnector. Error:', err2));\n                            }\n                        });\n                    this.updatePlayer(timingObject, player, options);\n                }\n                if ((await player.getPlaybackRate()) !== velocity) {\n                    player.setPlaybackRate(velocity);\n                }\n            }\n        }\n    }\n\n    /**\n     * Since video players do not play with 100% time precision, we need to closely monitor\n     * our player to be sure it remains in sync with the TimingObject.\n     *\n     * If out of sync, we use the current conditions and the options provided to determine\n     * whether to re-sync via setting currentTime or adjusting the playbackRate\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {{cancel: (function(): void)}}\n     */\n    maintainPlaybackPosition(timingObject, player, options) {\n        const { allowedDrift, maxAllowedDrift, minCheckInterval, maxRateAdjustment, maxTimeToCatchUp } = options;\n        const syncInterval = Math.min(maxTimeToCatchUp, Math.max(minCheckInterval, maxAllowedDrift)) * 1000;\n\n        const check = async() => {\n            if (timingObject.query().velocity === 0 || await player.getPaused() === true) {\n                return;\n            }\n            const diff = timingObject.query().position - (await player.getCurrentTime());\n            const diffAbs = Math.abs(diff);\n            this.log(`Drift: ${diff}`);\n            if (diffAbs > maxAllowedDrift) {\n                await this.adjustSpeed(player, 0);\n                player.setCurrentTime(timingObject.query().position);\n                this.log('Resync by currentTime');\n            }\n            else if (diffAbs > allowedDrift) {\n                const min = diffAbs / maxTimeToCatchUp;\n                const max = maxRateAdjustment;\n                const adjustment = min < max ? (max - min) / 2 : max;\n                await this.adjustSpeed(player, adjustment * Math.sign(diff));\n                this.log('Resync by playbackRate');\n            }\n        };\n        const interval = setInterval(() => check(), syncInterval);\n        return { cancel: () => clearInterval(interval) };\n    }\n\n    /**\n     * @param {string} msg\n     */\n    log(msg) {\n        this.logger?.(`TimingSrcConnector: ${msg}`);\n    }\n\n    speedAdjustment = 0;\n\n    /**\n     * @param {PlayerControls} player\n     * @param {number} newAdjustment\n     * @return {Promise<void>}\n     */\n    adjustSpeed = async(player, newAdjustment) => {\n        if (this.speedAdjustment === newAdjustment) {\n            return;\n        }\n        const newPlaybackRate = (await player.getPlaybackRate()) - this.speedAdjustment + newAdjustment;\n        this.log(`New playbackRate:  ${newPlaybackRate}`);\n        await player.setPlaybackRate(newPlaybackRate);\n        this.speedAdjustment = newAdjustment;\n    };\n\n    /**\n     * @param {TimingObject} timingObject\n     * @param {TConnectionState} state\n     * @return {Promise<void>}\n     */\n    waitForTOReadyState(timingObject, state) {\n        return new Promise((resolve) => {\n            const check = () => {\n                if (timingObject.readyState === state) {\n                    resolve();\n                }\n                else {\n                    timingObject.addEventListener('readystatechange', check, { once: true });\n                }\n            };\n            check();\n        });\n    }\n}\n", "import './lib/compatibility-check';\n\nimport 'weakmap-polyfill';\nimport Promise from 'native-promise-only';\n\nimport { storeCallback, getCallbacks, removeCallback, swapCallbacks } from './lib/callbacks';\nimport { getMethodName, isDomElement, isVimeoUrl, getVimeoUrl, isServerRuntime, logSurveyLink } from './lib/functions';\nimport {\n    getOEmbedParameters,\n    getOEmbedData,\n    createEmbed,\n    initializeEmbeds,\n    resizeEmbeds,\n    initAppendVideoMetadata,\n    checkUrlTimeParam,\n    updateDRMEmbeds\n} from './lib/embed';\nimport { parseMessageData, postMessage, processData } from './lib/postmessage';\nimport { initializeScreenfull } from './lib/screenfull.js';\nimport { TimingSrcConnector } from './lib/timing-src-connector';\n\nconst playerMap = new WeakMap();\nconst readyMap = new WeakMap();\nlet screenfull = {};\n\nclass Player {\n    /**\n     * Create a Player.\n     *\n     * @param {(HTMLIFrameElement|HTMLElement|string|jQuery)} element A reference to the Vimeo\n     *        player iframe, and id, or a jQuery object.\n     * @param {object} [options] oEmbed parameters to use when creating an embed in the element.\n     * @return {Player}\n     */\n    constructor(element, options = {}) {\n        this.allowLogging = options.logging || options.logging === undefined;\n\n        /* global jQuery */\n        if (window.jQuery && element instanceof jQuery) {\n            if (element.length > 1 && window.console && console.warn && this.allowLogging) {\n                console.warn('A jQuery object with multiple elements was passed, using the first element.');\n            }\n\n            element = element[0];\n        }\n\n        // Find an element by ID\n        if (typeof document !== 'undefined' && typeof element === 'string') {\n            element = document.getElementById(element);\n        }\n\n        // Not an element!\n        if (!isDomElement(element)) {\n            throw new TypeError('You must pass either a valid element or a valid id.');\n        }\n\n        // Already initialized an embed in this div, so grab the iframe\n        if (element.nodeName !== 'IFRAME') {\n            const iframe = element.querySelector('iframe');\n\n            if (iframe) {\n                element = iframe;\n            }\n        }\n\n        // iframe url is not a Vimeo url\n        if (element.nodeName === 'IFRAME' && !isVimeoUrl(element.getAttribute('src') || '')) {\n            throw new Error('The player element passed isn’t a Vimeo embed.');\n        }\n\n        // If there is already a player object in the map, return that\n        if (playerMap.has(element)) {\n            return playerMap.get(element);\n        }\n\n        this._window = element.ownerDocument.defaultView;\n        this.element = element;\n        this.origin = '*';\n\n        const readyPromise = new Promise((resolve, reject) => {\n            this._onMessage = (event) => {\n                if (!isVimeoUrl(event.origin) || this.element.contentWindow !== event.source) {\n                    return;\n                }\n\n                if (this.origin === '*') {\n                    this.origin = event.origin;\n                }\n\n                const data = parseMessageData(event.data);\n                const isError = data && data.event === 'error';\n                const isReadyError = isError && data.data && data.data.method === 'ready';\n\n                if (isReadyError) {\n                    const error = new Error(data.data.message);\n                    error.name = data.data.name;\n                    reject(error);\n                    return;\n                }\n\n                const isReadyEvent = data && data.event === 'ready';\n                const isPingResponse = data && data.method === 'ping';\n\n                if (isReadyEvent || isPingResponse) {\n                    this.element.setAttribute('data-ready', 'true');\n                    resolve();\n                    return;\n                }\n\n                processData(this, data);\n            };\n\n            this._window.addEventListener('message', this._onMessage);\n\n            if (this.element.nodeName !== 'IFRAME') {\n                const params = getOEmbedParameters(element, options);\n                const url = getVimeoUrl(params);\n\n                getOEmbedData(url, params, element).then((data) => {\n                    const iframe = createEmbed(data, element);\n                    // Overwrite element with the new iframe,\n                    // but store reference to the original element\n                    this.element = iframe;\n                    this._originalElement = element;\n\n                    swapCallbacks(element, iframe);\n                    playerMap.set(this.element, this);\n\n                    return data;\n                }).catch(reject);\n            }\n        });\n\n        // Store a copy of this Player in the map\n        readyMap.set(this, readyPromise);\n        playerMap.set(this.element, this);\n\n        // Send a ping to the iframe so the ready promise will be resolved if\n        // the player is already ready.\n        if (this.element.nodeName === 'IFRAME') {\n            postMessage(this, 'ping');\n        }\n\n        if (screenfull.isEnabled) {\n            const exitFullscreen = () => screenfull.exit();\n            this.fullscreenchangeHandler = () => {\n                if (screenfull.isFullscreen) {\n                    storeCallback(this, 'event:exitFullscreen', exitFullscreen);\n                }\n                else {\n                    removeCallback(this, 'event:exitFullscreen', exitFullscreen);\n                }\n                // eslint-disable-next-line\n                this.ready().then(() => {\n                    postMessage(this, 'fullscreenchange', screenfull.isFullscreen);\n                });\n            };\n\n            screenfull.on('fullscreenchange', this.fullscreenchangeHandler);\n        }\n\n        if (this.allowLogging) {\n            logSurveyLink();\n        }\n\n        return this;\n    }\n\n    /**\n     * Check to see if the URL is a Vimeo URL.\n     *\n     * @param {string} url The URL string.\n     * @return {boolean}\n     */\n    static isVimeoUrl(url) {\n        return isVimeoUrl(url);\n    }\n\n    /**\n     * Get a promise for a method.\n     *\n     * @param {string} name The API method to call.\n     * @param {...(string|number|object|Array)} args Arguments to send via postMessage.\n     * @return {Promise}\n     */\n    callMethod(name, ...args) {\n        if (name === undefined || name === null) {\n            throw new TypeError('You must pass a method name.');\n        }\n\n        return new Promise((resolve, reject) => {\n            // We are storing the resolve/reject handlers to call later, so we\n            // can’t return here.\n            return this.ready().then(() => {\n                storeCallback(this, name, {\n                    resolve,\n                    reject\n                });\n\n                if (args.length === 0) {\n                    args = {};\n                }\n                else if (args.length === 1) {\n                    args = args[0];\n                }\n\n                postMessage(this, name, args);\n            }).catch(reject);\n        });\n    }\n    /**\n     * Get a promise for the value of a player property.\n     *\n     * @param {string} name The property name\n     * @return {Promise}\n     */\n    get(name) {\n        return new Promise((resolve, reject) => {\n            name = getMethodName(name, 'get');\n\n            // We are storing the resolve/reject handlers to call later, so we\n            // can’t return here.\n            return this.ready().then(() => {\n                storeCallback(this, name, {\n                    resolve,\n                    reject\n                });\n\n                postMessage(this, name);\n            }).catch(reject);\n        });\n    }\n\n    /**\n     * Get a promise for setting the value of a player property.\n     *\n     * @param {string} name The API method to call.\n     * @param {mixed} value The value to set.\n     * @return {Promise}\n     */\n    set(name, value) {\n        return new Promise((resolve, reject) => {\n            name = getMethodName(name, 'set');\n\n            if (value === undefined || value === null) {\n                throw new TypeError('There must be a value to set.');\n            }\n\n            // We are storing the resolve/reject handlers to call later, so we\n            // can’t return here.\n            return this.ready().then(() => {\n                storeCallback(this, name, {\n                    resolve,\n                    reject\n                });\n\n                postMessage(this, name, value);\n            }).catch(reject);\n        });\n    }\n\n    /**\n     * Add an event listener for the specified event. Will call the\n     * callback with a single parameter, `data`, that contains the data for\n     * that event.\n     *\n     * @param {string} eventName The name of the event.\n     * @param {function(*)} callback The function to call when the event fires.\n     * @return {void}\n     */\n    on(eventName, callback) {\n        if (!eventName) {\n            throw new TypeError('You must pass an event name.');\n        }\n\n        if (!callback) {\n            throw new TypeError('You must pass a callback function.');\n        }\n\n        if (typeof callback !== 'function') {\n            throw new TypeError('The callback must be a function.');\n        }\n\n        const callbacks = getCallbacks(this, `event:${eventName}`);\n        if (callbacks.length === 0) {\n            this.callMethod('addEventListener', eventName).catch(() => {\n                // Ignore the error. There will be an error event fired that\n                // will trigger the error callback if they are listening.\n            });\n        }\n\n        storeCallback(this, `event:${eventName}`, callback);\n    }\n\n    /**\n     * Remove an event listener for the specified event. Will remove all\n     * listeners for that event if a `callback` isn’t passed, or only that\n     * specific callback if it is passed.\n     *\n     * @param {string} eventName The name of the event.\n     * @param {function} [callback] The specific callback to remove.\n     * @return {void}\n     */\n    off(eventName, callback) {\n        if (!eventName) {\n            throw new TypeError('You must pass an event name.');\n        }\n\n        if (callback && typeof callback !== 'function') {\n            throw new TypeError('The callback must be a function.');\n        }\n\n        const lastCallback = removeCallback(this, `event:${eventName}`, callback);\n\n        // If there are no callbacks left, remove the listener\n        if (lastCallback) {\n            this.callMethod('removeEventListener', eventName).catch((e) => {\n                // Ignore the error. There will be an error event fired that\n                // will trigger the error callback if they are listening.\n            });\n        }\n    }\n\n    /**\n     * A promise to load a new video.\n     *\n     * @promise LoadVideoPromise\n     * @fulfill {number} The video with this id or url successfully loaded.\n     * @reject {TypeError} The id was not a number.\n     */\n    /**\n     * Load a new video into this embed. The promise will be resolved if\n     * the video is successfully loaded, or it will be rejected if it could\n     * not be loaded.\n     *\n     * @param {number|string|object} options The id of the video, the url of the video, or an object with embed options.\n     * @return {LoadVideoPromise}\n     */\n    loadVideo(options) {\n        return this.callMethod('loadVideo', options);\n    }\n\n    /**\n     * A promise to perform an action when the Player is ready.\n     *\n     * @todo document errors\n     * @promise LoadVideoPromise\n     * @fulfill {void}\n     */\n    /**\n     * Trigger a function when the player iframe has initialized. You do not\n     * need to wait for `ready` to trigger to begin adding event listeners\n     * or calling other methods.\n     *\n     * @return {ReadyPromise}\n     */\n    ready() {\n        const readyPromise = readyMap.get(this) || new Promise((resolve, reject) => {\n            reject(new Error('Unknown player. Probably unloaded.'));\n        });\n        return Promise.resolve(readyPromise);\n    }\n\n    /**\n     * A promise to add a cue point to the player.\n     *\n     * @promise AddCuePointPromise\n     * @fulfill {string} The id of the cue point to use for removeCuePoint.\n     * @reject {RangeError} the time was less than 0 or greater than the\n     *         video’s duration.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Add a cue point to the player.\n     *\n     * @param {number} time The time for the cue point.\n     * @param {object} [data] Arbitrary data to be returned with the cue point.\n     * @return {AddCuePointPromise}\n     */\n    addCuePoint(time, data = {}) {\n        return this.callMethod('addCuePoint', { time, data });\n    }\n\n    /**\n     * A promise to remove a cue point from the player.\n     *\n     * @promise AddCuePointPromise\n     * @fulfill {string} The id of the cue point that was removed.\n     * @reject {InvalidCuePoint} The cue point with the specified id was not\n     *         found.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Remove a cue point from the video.\n     *\n     * @param {string} id The id of the cue point to remove.\n     * @return {RemoveCuePointPromise}\n     */\n    removeCuePoint(id) {\n        return this.callMethod('removeCuePoint', id);\n    }\n\n    /**\n     * A representation of a text track on a video.\n     *\n     * @typedef {Object} VimeoTextTrack\n     * @property {string} language The ISO language code.\n     * @property {string} kind The kind of track it is (captions or subtitles).\n     * @property {string} label The human‐readable label for the track.\n     */\n    /**\n     * A promise to enable a text track.\n     *\n     * @promise EnableTextTrackPromise\n     * @fulfill {VimeoTextTrack} The text track that was enabled.\n     * @reject {InvalidTrackLanguageError} No track was available with the\n     *         specified language.\n     * @reject {InvalidTrackError} No track was available with the specified\n     *         language and kind.\n     */\n    /**\n     * Enable the text track with the specified language, and optionally the\n     * specified kind (captions or subtitles).\n     *\n     * When set via the API, the track language will not change the viewer’s\n     * stored preference.\n     *\n     * @param {string} language The two‐letter language code.\n     * @param {string} [kind] The kind of track to enable (captions or subtitles).\n     * @return {EnableTextTrackPromise}\n     */\n    enableTextTrack(language, kind) {\n        if (!language) {\n            throw new TypeError('You must pass a language.');\n        }\n\n        return this.callMethod('enableTextTrack', {\n            language,\n            kind\n        });\n    }\n\n    /**\n     * A promise to disable the active text track.\n     *\n     * @promise DisableTextTrackPromise\n     * @fulfill {void} The track was disabled.\n     */\n    /**\n     * Disable the currently-active text track.\n     *\n     * @return {DisableTextTrackPromise}\n     */\n    disableTextTrack() {\n        return this.callMethod('disableTextTrack');\n    }\n\n    /**\n     * A promise to pause the video.\n     *\n     * @promise PausePromise\n     * @fulfill {void} The video was paused.\n     */\n    /**\n     * Pause the video if it’s playing.\n     *\n     * @return {PausePromise}\n     */\n    pause() {\n        return this.callMethod('pause');\n    }\n\n    /**\n     * A promise to play the video.\n     *\n     * @promise PlayPromise\n     * @fulfill {void} The video was played.\n     */\n    /**\n     * Play the video if it’s paused. **Note:** on iOS and some other\n     * mobile devices, you cannot programmatically trigger play. Once the\n     * viewer has tapped on the play button in the player, however, you\n     * will be able to use this function.\n     *\n     * @return {PlayPromise}\n     */\n    play() {\n        return this.callMethod('play');\n    }\n\n    /**\n     * Request that the player enters fullscreen.\n     * @return {Promise}\n     */\n    requestFullscreen() {\n        if (screenfull.isEnabled) {\n            return screenfull.request(this.element);\n        }\n        return this.callMethod('requestFullscreen');\n    }\n\n    /**\n     * Request that the player exits fullscreen.\n     * @return {Promise}\n     */\n    exitFullscreen() {\n        if (screenfull.isEnabled) {\n            return screenfull.exit();\n        }\n        return this.callMethod('exitFullscreen');\n    }\n\n    /**\n     * Returns true if the player is currently fullscreen.\n     * @return {Promise}\n     */\n    getFullscreen() {\n        if (screenfull.isEnabled) {\n            return Promise.resolve(screenfull.isFullscreen);\n        }\n        return this.get('fullscreen');\n    }\n\n    /**\n     * Request that the player enters picture-in-picture.\n     * @return {Promise}\n     */\n    requestPictureInPicture() {\n        return this.callMethod('requestPictureInPicture');\n    }\n\n    /**\n     * Request that the player exits picture-in-picture.\n     * @return {Promise}\n     */\n    exitPictureInPicture() {\n        return this.callMethod('exitPictureInPicture');\n    }\n\n    /**\n     * Returns true if the player is currently picture-in-picture.\n     * @return {Promise}\n     */\n    getPictureInPicture() {\n        return this.get('pictureInPicture');\n    }\n\n    /**\n     * A promise to prompt the viewer to initiate remote playback.\n     *\n     * @promise RemotePlaybackPromptPromise\n     * @fulfill {void}\n     * @reject {NotFoundError} No remote playback device is available.\n     */\n    /**\n     * Request to prompt the user to initiate remote playback.\n     *\n     * @return {RemotePlaybackPromptPromise}\n     */\n    remotePlaybackPrompt() {\n        return this.callMethod('remotePlaybackPrompt');\n    }\n\n    /**\n     * A promise to unload the video.\n     *\n     * @promise UnloadPromise\n     * @fulfill {void} The video was unloaded.\n     */\n    /**\n     * Return the player to its initial state.\n     *\n     * @return {UnloadPromise}\n     */\n    unload() {\n        return this.callMethod('unload');\n    }\n\n    /**\n     * Cleanup the player and remove it from the DOM\n     *\n     * It won't be usable and a new one should be constructed\n     *  in order to do any operations.\n     *\n     * @return {Promise}\n     */\n    destroy() {\n        return new Promise((resolve) => {\n            readyMap.delete(this);\n            playerMap.delete(this.element);\n\n            if (this._originalElement) {\n                playerMap.delete(this._originalElement);\n                this._originalElement.removeAttribute('data-vimeo-initialized');\n            }\n\n            if (this.element && this.element.nodeName === 'IFRAME' && this.element.parentNode) {\n                // If we've added an additional wrapper div, remove that from the DOM.\n                // If not, just remove the iframe element.\n                if (this.element.parentNode.parentNode && this._originalElement && this._originalElement !== this.element.parentNode) {\n                    this.element.parentNode.parentNode.removeChild(this.element.parentNode);\n                }\n                else {\n                    this.element.parentNode.removeChild(this.element);\n                }\n            }\n\n            // If the clip is private there is a case where the element stays the\n            // div element. Destroy should reset the div and remove the iframe child.\n            if (this.element && this.element.nodeName === 'DIV' && this.element.parentNode) {\n                this.element.removeAttribute('data-vimeo-initialized');\n                const iframe = this.element.querySelector('iframe');\n                if (iframe && iframe.parentNode) {\n                    // If we've added an additional wrapper div, remove that from the DOM.\n                    // If not, just remove the iframe element.\n                    if (iframe.parentNode.parentNode && this._originalElement && this._originalElement !== iframe.parentNode) {\n                        iframe.parentNode.parentNode.removeChild(iframe.parentNode);\n                    }\n                    else {\n                        iframe.parentNode.removeChild(iframe);\n                    }\n                }\n            }\n\n            this._window.removeEventListener('message', this._onMessage);\n\n            if (screenfull.isEnabled) {\n                screenfull.off('fullscreenchange', this.fullscreenchangeHandler);\n            }\n\n            resolve();\n        });\n    }\n\n    /**\n     * A promise to get the autopause behavior of the video.\n     *\n     * @promise GetAutopausePromise\n     * @fulfill {boolean} Whether autopause is turned on or off.\n     * @reject {UnsupportedError} Autopause is not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Get the autopause behavior for this player.\n     *\n     * @return {GetAutopausePromise}\n     */\n    getAutopause() {\n        return this.get('autopause');\n    }\n\n    /**\n     * A promise to set the autopause behavior of the video.\n     *\n     * @promise SetAutopausePromise\n     * @fulfill {boolean} Whether autopause is turned on or off.\n     * @reject {UnsupportedError} Autopause is not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Enable or disable the autopause behavior of this player.\n     *\n     * By default, when another video is played in the same browser, this\n     * player will automatically pause. Unless you have a specific reason\n     * for doing so, we recommend that you leave autopause set to the\n     * default (`true`).\n     *\n     * @param {boolean} autopause\n     * @return {SetAutopausePromise}\n     */\n    setAutopause(autopause) {\n        return this.set('autopause', autopause);\n    }\n\n    /**\n     * A promise to get the buffered property of the video.\n     *\n     * @promise GetBufferedPromise\n     * @fulfill {Array} Buffered Timeranges converted to an Array.\n     */\n    /**\n     * Get the buffered property of the video.\n     *\n     * @return {GetBufferedPromise}\n     */\n    getBuffered() {\n        return this.get('buffered');\n    }\n\n    /**\n     * @typedef {Object} CameraProperties\n     * @prop {number} props.yaw - Number between 0 and 360.\n     * @prop {number} props.pitch - Number between -90 and 90.\n     * @prop {number} props.roll - Number between -180 and 180.\n     * @prop {number} props.fov - The field of view in degrees.\n     */\n    /**\n     * A promise to get the camera properties of the player.\n     *\n     * @promise GetCameraPromise\n     * @fulfill {CameraProperties} The camera properties.\n     */\n    /**\n     * For 360° videos get the camera properties for this player.\n     *\n     * @return {GetCameraPromise}\n     */\n    getCameraProps() {\n        return this.get('cameraProps');\n    }\n\n    /**\n     * A promise to set the camera properties of the player.\n     *\n     * @promise SetCameraPromise\n     * @fulfill {Object} The camera was successfully set.\n     * @reject {RangeError} The range was out of bounds.\n     */\n    /**\n     * For 360° videos set the camera properties for this player.\n     *\n     * @param {CameraProperties} camera The camera properties\n     * @return {SetCameraPromise}\n     */\n    setCameraProps(camera) {\n        return this.set('cameraProps', camera);\n    }\n\n    /**\n     * A representation of a chapter.\n     *\n     * @typedef {Object} VimeoChapter\n     * @property {number} startTime The start time of the chapter.\n     * @property {object} title The title of the chapter.\n     * @property {number} index The place in the order of Chapters. Starts at 1.\n     */\n    /**\n     * A promise to get chapters for the video.\n     *\n     * @promise GetChaptersPromise\n     * @fulfill {VimeoChapter[]} The chapters for the video.\n     */\n    /**\n     * Get an array of all the chapters for the video.\n     *\n     * @return {GetChaptersPromise}\n     */\n    getChapters() {\n        return this.get('chapters');\n    }\n\n    /**\n     * A promise to get the currently active chapter.\n     *\n     * @promise GetCurrentChaptersPromise\n     * @fulfill {VimeoChapter|undefined} The current chapter for the video.\n     */\n    /**\n     * Get the currently active chapter for the video.\n     *\n     * @return {GetCurrentChaptersPromise}\n     */\n    getCurrentChapter() {\n        return this.get('currentChapter');\n    }\n\n    /**\n     * A promise to get the accent color of the player.\n     *\n     * @promise GetColorPromise\n     * @fulfill {string} The hex color of the player.\n     */\n    /**\n     * Get the accent color for this player. Note this is deprecated in place of `getColorTwo`.\n     *\n     * @return {GetColorPromise}\n     */\n    getColor() {\n        return this.get('color');\n    }\n\n    /**\n     * A promise to get all colors for the player in an array.\n     *\n     * @promise GetColorsPromise\n     * @fulfill {string[]} The hex colors of the player.\n     */\n    /**\n     * Get all the colors for this player in an array: [colorOne, colorTwo, colorThree, colorFour]\n     *\n     * @return {GetColorPromise}\n     */\n    getColors() {\n        return Promise.all([\n            this.get('colorOne'),\n            this.get('colorTwo'),\n            this.get('colorThree'),\n            this.get('colorFour')\n        ]);\n    }\n\n    /**\n     * A promise to set the accent color of the player.\n     *\n     * @promise SetColorPromise\n     * @fulfill {string} The color was successfully set.\n     * @reject {TypeError} The string was not a valid hex or rgb color.\n     * @reject {ContrastError} The color was set, but the contrast is\n     *         outside of the acceptable range.\n     * @reject {EmbedSettingsError} The owner of the player has chosen to\n     *         use a specific color.\n     */\n    /**\n     * Set the accent color of this player to a hex or rgb string. Setting the\n     * color may fail if the owner of the video has set their embed\n     * preferences to force a specific color.\n     * Note this is deprecated in place of `setColorTwo`.\n     *\n     * @param {string} color The hex or rgb color string to set.\n     * @return {SetColorPromise}\n     */\n    setColor(color) {\n        return this.set('color', color);\n    }\n\n    /**\n     * A promise to set all colors for the player.\n     *\n     * @promise SetColorsPromise\n     * @fulfill {string[]} The colors were successfully set.\n     * @reject {TypeError} The string was not a valid hex or rgb color.\n     * @reject {ContrastError} The color was set, but the contrast is\n     *         outside of the acceptable range.\n     * @reject {EmbedSettingsError} The owner of the player has chosen to\n     *         use a specific color.\n     */\n    /**\n     * Set the colors of this player to a hex or rgb string. Setting the\n     * color may fail if the owner of the video has set their embed\n     * preferences to force a specific color.\n     * The colors should be passed in as an array: [colorOne, colorTwo, colorThree, colorFour].\n     * If a color should not be set, the index in the array can be left as null.\n     *\n     * @param {string[]} colors Array of the hex or rgb color strings to set.\n     * @return {SetColorsPromise}\n     */\n    setColors(colors) {\n        if (!Array.isArray(colors)) {\n            return new Promise((resolve, reject) => reject(new TypeError('Argument must be an array.')));\n        }\n\n        const nullPromise = new Promise((resolve) => resolve(null));\n        const colorPromises = [\n            colors[0] ? this.set('colorOne', colors[0]) : nullPromise,\n            colors[1] ? this.set('colorTwo', colors[1]) : nullPromise,\n            colors[2] ? this.set('colorThree', colors[2]) : nullPromise,\n            colors[3] ? this.set('colorFour', colors[3]) : nullPromise\n        ];\n        return Promise.all(colorPromises);\n    }\n\n    /**\n     * A representation of a cue point.\n     *\n     * @typedef {Object} VimeoCuePoint\n     * @property {number} time The time of the cue point.\n     * @property {object} data The data passed when adding the cue point.\n     * @property {string} id The unique id for use with removeCuePoint.\n     */\n    /**\n     * A promise to get the cue points of a video.\n     *\n     * @promise GetCuePointsPromise\n     * @fulfill {VimeoCuePoint[]} The cue points added to the video.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Get an array of the cue points added to the video.\n     *\n     * @return {GetCuePointsPromise}\n     */\n    getCuePoints() {\n        return this.get('cuePoints');\n    }\n\n    /**\n     * A promise to get the current time of the video.\n     *\n     * @promise GetCurrentTimePromise\n     * @fulfill {number} The current time in seconds.\n     */\n    /**\n     * Get the current playback position in seconds.\n     *\n     * @return {GetCurrentTimePromise}\n     */\n    getCurrentTime() {\n        return this.get('currentTime');\n    }\n\n    /**\n     * A promise to set the current time of the video.\n     *\n     * @promise SetCurrentTimePromise\n     * @fulfill {number} The actual current time that was set.\n     * @reject {RangeError} the time was less than 0 or greater than the\n     *         video’s duration.\n     */\n    /**\n     * Set the current playback position in seconds. If the player was\n     * paused, it will remain paused. Likewise, if the player was playing,\n     * it will resume playing once the video has buffered.\n     *\n     * You can provide an accurate time and the player will attempt to seek\n     * to as close to that time as possible. The exact time will be the\n     * fulfilled value of the promise.\n     *\n     * @param {number} currentTime\n     * @return {SetCurrentTimePromise}\n     */\n    setCurrentTime(currentTime) {\n        return this.set('currentTime', currentTime);\n    }\n\n    /**\n     * A promise to get the duration of the video.\n     *\n     * @promise GetDurationPromise\n     * @fulfill {number} The duration in seconds.\n     */\n    /**\n     * Get the duration of the video in seconds. It will be rounded to the\n     * nearest second before playback begins, and to the nearest thousandth\n     * of a second after playback begins.\n     *\n     * @return {GetDurationPromise}\n     */\n    getDuration() {\n        return this.get('duration');\n    }\n\n    /**\n     * A promise to get the ended state of the video.\n     *\n     * @promise GetEndedPromise\n     * @fulfill {boolean} Whether or not the video has ended.\n     */\n    /**\n     * Get the ended state of the video. The video has ended if\n     * `currentTime === duration`.\n     *\n     * @return {GetEndedPromise}\n     */\n    getEnded() {\n        return this.get('ended');\n    }\n\n    /**\n     * A promise to get the loop state of the player.\n     *\n     * @promise GetLoopPromise\n     * @fulfill {boolean} Whether or not the player is set to loop.\n     */\n    /**\n     * Get the loop state of the player.\n     *\n     * @return {GetLoopPromise}\n     */\n    getLoop() {\n        return this.get('loop');\n    }\n\n    /**\n     * A promise to set the loop state of the player.\n     *\n     * @promise SetLoopPromise\n     * @fulfill {boolean} The loop state that was set.\n     */\n    /**\n     * Set the loop state of the player. When set to `true`, the player\n     * will start over immediately once playback ends.\n     *\n     * @param {boolean} loop\n     * @return {SetLoopPromise}\n     */\n    setLoop(loop) {\n        return this.set('loop', loop);\n    }\n\n\n    /**\n     * A promise to set the muted state of the player.\n     *\n     * @promise SetMutedPromise\n     * @fulfill {boolean} The muted state that was set.\n     */\n    /**\n     * Set the muted state of the player. When set to `true`, the player\n     * volume will be muted.\n     *\n     * @param {boolean} muted\n     * @return {SetMutedPromise}\n     */\n    setMuted(muted) {\n        return this.set('muted', muted);\n    }\n\n    /**\n     * A promise to get the muted state of the player.\n     *\n     * @promise GetMutedPromise\n     * @fulfill {boolean} Whether or not the player is muted.\n     */\n    /**\n     * Get the muted state of the player.\n     *\n     * @return {GetMutedPromise}\n     */\n    getMuted() {\n        return this.get('muted');\n    }\n\n    /**\n     * A promise to get the paused state of the player.\n     *\n     * @promise GetLoopPromise\n     * @fulfill {boolean} Whether or not the video is paused.\n     */\n    /**\n     * Get the paused state of the player.\n     *\n     * @return {GetLoopPromise}\n     */\n    getPaused() {\n        return this.get('paused');\n    }\n\n    /**\n     * A promise to get the playback rate of the player.\n     *\n     * @promise GetPlaybackRatePromise\n     * @fulfill {number} The playback rate of the player on a scale from 0 to 2.\n     */\n    /**\n     * Get the playback rate of the player on a scale from `0` to `2`.\n     *\n     * @return {GetPlaybackRatePromise}\n     */\n    getPlaybackRate() {\n        return this.get('playbackRate');\n    }\n\n    /**\n     * A promise to set the playbackrate of the player.\n     *\n     * @promise SetPlaybackRatePromise\n     * @fulfill {number} The playback rate was set.\n     * @reject {RangeError} The playback rate was less than 0 or greater than 2.\n     */\n    /**\n     * Set the playback rate of the player on a scale from `0` to `2`. When set\n     * via the API, the playback rate will not be synchronized to other\n     * players or stored as the viewer's preference.\n     *\n     * @param {number} playbackRate\n     * @return {SetPlaybackRatePromise}\n     */\n    setPlaybackRate(playbackRate) {\n        return this.set('playbackRate', playbackRate);\n    }\n\n    /**\n     * A promise to get the played property of the video.\n     *\n     * @promise GetPlayedPromise\n     * @fulfill {Array} Played Timeranges converted to an Array.\n     */\n    /**\n     * Get the played property of the video.\n     *\n     * @return {GetPlayedPromise}\n     */\n    getPlayed() {\n        return this.get('played');\n    }\n\n    /**\n     * A promise to get the qualities available of the current video.\n     *\n     * @promise GetQualitiesPromise\n     * @fulfill {Array} The qualities of the video.\n     */\n    /**\n     * Get the qualities of the current video.\n     *\n     * @return {GetQualitiesPromise}\n     */\n    getQualities() {\n        return this.get('qualities');\n    }\n\n    /**\n     * A promise to get the current set quality of the video.\n     *\n     * @promise GetQualityPromise\n     * @fulfill {string} The current set quality.\n     */\n    /**\n     * Get the current set quality of the video.\n     *\n     * @return {GetQualityPromise}\n     */\n    getQuality() {\n        return this.get('quality');\n    }\n\n    /**\n     * A promise to set the video quality.\n     *\n     * @promise SetQualityPromise\n     * @fulfill {number} The quality was set.\n     * @reject {RangeError} The quality is not available.\n     */\n    /**\n     * Set a video quality.\n     *\n     * @param {string} quality\n     * @return {SetQualityPromise}\n     */\n    setQuality(quality) {\n        return this.set('quality', quality);\n    }\n\n    /**\n     * A promise to get the remote playback availability.\n     *\n     * @promise RemotePlaybackAvailabilityPromise\n     * @fulfill {boolean} Whether remote playback is available.\n     */\n    /**\n     * Get the availability of remote playback.\n     *\n     * @return {RemotePlaybackAvailabilityPromise}\n     */\n    getRemotePlaybackAvailability() {\n        return this.get('remotePlaybackAvailability');\n    }\n\n    /**\n     * A promise to get the current remote playback state.\n     *\n     * @promise RemotePlaybackStatePromise\n     * @fulfill {string} The state of the remote playback: connecting, connected, or disconnected.\n     */\n    /**\n     * Get the current remote playback state.\n     *\n     * @return {RemotePlaybackStatePromise}\n     */\n    getRemotePlaybackState() {\n        return this.get('remotePlaybackState');\n    }\n\n    /**\n     * A promise to get the seekable property of the video.\n     *\n     * @promise GetSeekablePromise\n     * @fulfill {Array} Seekable Timeranges converted to an Array.\n     */\n    /**\n     * Get the seekable property of the video.\n     *\n     * @return {GetSeekablePromise}\n     */\n    getSeekable() {\n        return this.get('seekable');\n    }\n\n    /**\n     * A promise to get the seeking property of the player.\n     *\n     * @promise GetSeekingPromise\n     * @fulfill {boolean} Whether or not the player is currently seeking.\n     */\n    /**\n     * Get if the player is currently seeking.\n     *\n     * @return {GetSeekingPromise}\n     */\n    getSeeking() {\n        return this.get('seeking');\n    }\n\n    /**\n     * A promise to get the text tracks of a video.\n     *\n     * @promise GetTextTracksPromise\n     * @fulfill {VimeoTextTrack[]} The text tracks associated with the video.\n     */\n    /**\n     * Get an array of the text tracks that exist for the video.\n     *\n     * @return {GetTextTracksPromise}\n     */\n    getTextTracks() {\n        return this.get('textTracks');\n    }\n\n    /**\n     * A promise to get the embed code for the video.\n     *\n     * @promise GetVideoEmbedCodePromise\n     * @fulfill {string} The `<iframe>` embed code for the video.\n     */\n    /**\n     * Get the `<iframe>` embed code for the video.\n     *\n     * @return {GetVideoEmbedCodePromise}\n     */\n    getVideoEmbedCode() {\n        return this.get('videoEmbedCode');\n    }\n\n    /**\n     * A promise to get the id of the video.\n     *\n     * @promise GetVideoIdPromise\n     * @fulfill {number} The id of the video.\n     */\n    /**\n     * Get the id of the video.\n     *\n     * @return {GetVideoIdPromise}\n     */\n    getVideoId() {\n        return this.get('videoId');\n    }\n\n    /**\n     * A promise to get the title of the video.\n     *\n     * @promise GetVideoTitlePromise\n     * @fulfill {number} The title of the video.\n     */\n    /**\n     * Get the title of the video.\n     *\n     * @return {GetVideoTitlePromise}\n     */\n    getVideoTitle() {\n        return this.get('videoTitle');\n    }\n\n    /**\n     * A promise to get the native width of the video.\n     *\n     * @promise GetVideoWidthPromise\n     * @fulfill {number} The native width of the video.\n     */\n    /**\n     * Get the native width of the currently‐playing video. The width of\n     * the highest‐resolution available will be used before playback begins.\n     *\n     * @return {GetVideoWidthPromise}\n     */\n    getVideoWidth() {\n        return this.get('videoWidth');\n    }\n\n    /**\n     * A promise to get the native height of the video.\n     *\n     * @promise GetVideoHeightPromise\n     * @fulfill {number} The native height of the video.\n     */\n    /**\n     * Get the native height of the currently‐playing video. The height of\n     * the highest‐resolution available will be used before playback begins.\n     *\n     * @return {GetVideoHeightPromise}\n     */\n    getVideoHeight() {\n        return this.get('videoHeight');\n    }\n\n    /**\n     * A promise to get the vimeo.com url for the video.\n     *\n     * @promise GetVideoUrlPromise\n     * @fulfill {number} The vimeo.com url for the video.\n     * @reject {PrivacyError} The url isn’t available because of the video’s privacy setting.\n     */\n    /**\n     * Get the vimeo.com url for the video.\n     *\n     * @return {GetVideoUrlPromise}\n     */\n    getVideoUrl() {\n        return this.get('videoUrl');\n    }\n\n    /**\n     * A promise to get the volume level of the player.\n     *\n     * @promise GetVolumePromise\n     * @fulfill {number} The volume level of the player on a scale from 0 to 1.\n     */\n    /**\n     * Get the current volume level of the player on a scale from `0` to `1`.\n     *\n     * Most mobile devices do not support an independent volume from the\n     * system volume. In those cases, this method will always return `1`.\n     *\n     * @return {GetVolumePromise}\n     */\n    getVolume() {\n        return this.get('volume');\n    }\n\n    /**\n     * A promise to set the volume level of the player.\n     *\n     * @promise SetVolumePromise\n     * @fulfill {number} The volume was set.\n     * @reject {RangeError} The volume was less than 0 or greater than 1.\n     */\n    /**\n     * Set the volume of the player on a scale from `0` to `1`. When set\n     * via the API, the volume level will not be synchronized to other\n     * players or stored as the viewer’s preference.\n     *\n     * Most mobile devices do not support setting the volume. An error will\n     * *not* be triggered in that situation.\n     *\n     * @param {number} volume\n     * @return {SetVolumePromise}\n     */\n    setVolume(volume) {\n        return this.set('volume', volume);\n    }\n\n    /** @typedef {import('timing-object').ITimingObject} TimingObject */\n    /** @typedef {import('./lib/timing-src-connector.types').TimingSrcConnectorOptions} TimingSrcConnectorOptions */\n    /** @typedef {import('./lib/timing-src-connector').TimingSrcConnector} TimingSrcConnector */\n\n    /**\n     * Connects a TimingObject to the video player (https://webtiming.github.io/timingobject/)\n     *\n     * @param {TimingObject} timingObject\n     * @param {TimingSrcConnectorOptions} options\n     *\n     * @return {Promise<TimingSrcConnector>}\n     */\n    async setTimingSrc(timingObject, options) {\n        if (!timingObject) {\n            throw new TypeError('A Timing Object must be provided.');\n        }\n\n        await this.ready();\n        const connector = new TimingSrcConnector(this, timingObject, options);\n        postMessage(this, 'notifyTimingObjectConnect');\n        connector.addEventListener('disconnect', () => postMessage(this, 'notifyTimingObjectDisconnect'));\n\n        return connector;\n    }\n}\n\n// Setup embed only if this is not a server runtime\nif (!isServerRuntime) {\n    screenfull = initializeScreenfull();\n    initializeEmbeds();\n    resizeEmbeds();\n    initAppendVideoMetadata();\n    checkUrlTimeParam();\n    updateDRMEmbeds();\n}\n\nexport default Player;\n"], "names": ["isNode", "global", "toString", "call", "isBun", "<PERSON>un", "isDeno", "<PERSON><PERSON>", "isServerRuntime", "getMethodName", "prop", "type", "indexOf", "toLowerCase", "concat", "substr", "toUpperCase", "isDomElement", "element", "Boolean", "nodeType", "ownerDocument", "defaultView", "isInteger", "value", "isNaN", "parseFloat", "isFinite", "Math", "floor", "isVimeoUrl", "url", "test", "isVimeoEmbed", "expr", "getOembedDomain", "match", "domain", "replace", "customDomains", "_i", "_customDomains", "length", "customDomain", "endsWith", "getVimeoUrl", "oEmbedParameters", "arguments", "undefined", "id", "idOrUrl", "Error", "TypeError", "subscribe", "target", "eventName", "callback", "onName", "offName", "eventNames", "for<PERSON>ach", "evName", "cancel", "logSurveyLink", "console", "log", "findIframeBySourceWindow", "sourceWindow", "doc", "document", "querySelectorAll", "iframes", "i", "contentWindow", "arrayIndexOfSupport", "Array", "prototype", "postMessageSupport", "window", "postMessage", "self", "WeakMap", "hasOwnProperty", "Object", "hasDefine", "defineProperty", "x", "e", "object", "name", "configurable", "writable", "genId", "key", "checkInstance", "isObject", "entry", "_id", "methodName", "prefix", "rand", "random", "substring", "globalThis", "this", "UMD", "context", "definition", "module", "exports", "DEF", "builtInProp", "cycle", "scheduling_queue", "ToString", "timer", "setImmediate", "fn", "setTimeout", "obj", "val", "config", "err", "Queue", "first", "last", "item", "<PERSON><PERSON>", "next", "add", "drain", "f", "schedule", "isThenable", "o", "_then", "o_type", "then", "notify", "chain", "notifyIsolated", "state", "success", "failure", "cb", "ret", "reject", "msg", "promise", "resolve", "triggered", "def", "def_wrapper", "MakeDefWrapper", "$resolve$", "apply", "$reject$", "iteratePromises", "<PERSON><PERSON><PERSON><PERSON>", "arr", "resolver", "rejecter", "idx", "IIFE", "$resolver$", "MakeDef", "Promise", "executor", "__NPO__", "constructor", "extractChain", "push", "$catch$", "publicResolve", "publicReject", "PromisePrototype", "Promise$resolve", "Promise$reject", "Promise$all", "len", "msgs", "count", "Promise$race", "callbackMap", "storeCallback", "player", "playerCallbacks", "get", "set", "getCallbacks", "removeCallback", "index", "splice", "shiftCallbacks", "shift", "swapCallbacks", "oldElement", "newElement", "delete", "parseMessageData", "data", "JSON", "parse", "error", "warn", "method", "params", "message", "ieVersion", "navigator", "userAgent", "stringify", "origin", "processData", "callbacks", "param", "event", "promises", "getOEmbedParameters", "defaults", "reduce", "getAttribute", "createEmbed", "_ref", "html", "querySelector", "div", "createElement", "innerHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setAttribute", "getOEmbedData", "videoUrl", "encodeURIComponent", "xhr", "XDomainRequest", "XMLHttpRequest", "open", "onload", "status", "json", "responseText", "domain_status_code", "onerror", "send", "initializeEmbeds", "parent", "elements", "slice", "handleError", "catch", "resizeEmbeds", "VimeoPlayerResizeEmbeds_", "onMessage", "sender<PERSON><PERSON><PERSON>", "source", "space", "parentElement", "style", "paddingBottom", "bottom", "addEventListener", "initAppendVideoMetadata", "VimeoSeoMetadataAppended", "src", "Player", "callMethod", "location", "href", "checkUrlTimeParam", "VimeoCheckedUrlTimeParam", "getVideoId", "videoId", "matches", "RegExp", "exec", "sec", "decodeURI", "setCurrentTime", "updateDRMEmbeds", "VimeoDRMEmbedsUpdated", "currentAllow", "allowSupportsDRM", "includes", "currentUrl", "URL", "searchParams", "initializeScreenfull", "fnMap", "l", "eventNameMap", "fullscreenchange", "fullscreenerror", "screenfull", "request", "onFullScreenEntered", "off", "on", "documentElement", "returnPromise", "requestFullscreen", "exit", "isFullscreen", "onFullScreenExit", "exitFullscreen", "removeEventListener", "defineProperties", "fullscreenElement", "enumerable", "isEnabled", "fullscreenEnabled", "defaultOptions", "role", "autoPlayMuted", "allowedDrift", "maxAllowedDrift", "minCheckInterval", "maxRateAdjustment", "maxTimeToCatchUp", "TimingSrcConnector", "_EventTarget", "_inherits", "_super", "_createSuper", "timingObject", "_this", "options", "logger", "_classCallCheck", "_defineProperty", "_assertThisInitialized", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "newAdjustment", "newPlaybackRate", "wrap", "_callee$", "_context", "prev", "speedAdjustment", "abrupt", "getPlaybackRate", "t0", "sent", "t1", "t2", "t3", "setPlaybackRate", "stop", "_x", "_x2", "init", "_objectSpread", "_createClass", "disconnect", "dispatchEvent", "Event", "_init", "_callee2", "_this2", "playerUpdater", "positionSync", "timingObjectUpdater", "_callee2$", "_context2", "waitForTOReadyState", "updatePlayer", "maintainPlaybackPosition", "updateTimingObject", "_x3", "_x4", "_x5", "_updateTimingObject", "_callee3", "_yield$Promise$all", "_yield$Promise$all2", "position", "isPaused", "playbackRate", "_callee3$", "_context3", "all", "getCurrentTime", "getPaused", "_slicedToArray", "update", "velocity", "_x6", "_x7", "_updatePlayer", "_callee5", "_timingObject$query", "_callee5$", "_context5", "query", "pause", "play", "_ref2", "_callee4", "_callee4$", "_context4", "setMuted", "err2", "allowLogging", "_x11", "_x8", "_x9", "_x10", "_this3", "syncInterval", "min", "max", "check", "_ref3", "_callee6", "diff", "diffAbs", "adjustment", "_callee6$", "_context6", "abs", "adjustSpeed", "sign", "interval", "setInterval", "clearInterval", "_this$logger", "readyState", "once", "_wrapNativeSuper", "EventTarget", "playerMap", "readyMap", "logging", "j<PERSON><PERSON><PERSON>", "getElementById", "nodeName", "iframe", "has", "_window", "readyPromise", "_onMessage", "isError", "isReadyError", "isReadyEvent", "isPingResponse", "_originalElement", "fullscreenchangeHandler", "ready", "_len", "args", "_key", "_this4", "lastCallback", "loadVideo", "addCuePoint", "time", "removeCuePoint", "enableTextTrack", "language", "kind", "disableTextTrack", "getFullscreen", "requestPictureInPicture", "exitPictureInPicture", "getPictureInPicture", "remotePlaybackPrompt", "unload", "destroy", "_this5", "removeAttribute", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "getAutopause", "set<PERSON><PERSON>pa<PERSON>", "autopause", "getBuffered", "getCameraProps", "setCameraProps", "camera", "getChapters", "getCurrentChapter", "getColor", "getColors", "setColor", "color", "setColors", "colors", "isArray", "nullPromise", "colorPromises", "getCuePoints", "currentTime", "getDuration", "getEnded", "getLoop", "setLoop", "loop", "muted", "getMuted", "getPlayed", "getQualities", "getQuality", "setQuality", "quality", "getRemotePlaybackAvailability", "getRemotePlaybackState", "getSeekable", "getSeeking", "getTextTracks", "getVideoEmbedCode", "getVideoTitle", "getVideoWidth", "getVideoHeight", "getVideoUrl", "getVolume", "setVolume", "volume", "_setTimingSrc", "_this6", "connector", "setTimingSrc"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACO,IAAMA,MAAM,GAAG,OAAOC,MAAM,KAAK,WAAW,IAChD,EAAE,CAAEC,QAAQ,CAACC,IAAI,CAACF,MAAM,CAAC,KAAK,iBAAiB;;EAElD;EACA;EACA;EACA;EACA;EACO,IAAMG,KAAK,GAAG,OAAOC,GAAG,KAAK,WAAW;;EAE/C;EACA;EACA;EACA;EACA;EACO,IAAMC,MAAM,GAAG,OAAOC,IAAI,KAAK,WAAW;;EAEjD;EACA;EACA;EACA;EACO,IAAMC,eAAe,GAAGR,MAAM,IAAII,KAAK,IAAIE,MAAM;;EAExD;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASG,aAAaA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACtC,IAAID,IAAI,CAACE,OAAO,CAACD,IAAI,CAACE,WAAW,EAAE,CAAC,KAAK,CAAC,EAAE;MACxC,OAAOH,IAAI;;IAGf,UAAAI,MAAA,CAAUH,IAAI,CAACE,WAAW,EAAE,EAAAC,MAAA,CAAGJ,IAAI,CAACK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE,EAAAF,MAAA,CAAGJ,IAAI,CAACK,MAAM,CAAC,CAAC,CAAC;EACnF;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO,SAASE,YAAYA,CAACC,OAAO,EAAE;IAClC,OAAOC,OAAO,CACVD,OAAO,IAAIA,OAAO,CAACE,QAAQ,KAAK,CAAC,IAAI,UAAU,IAAIF,OAAO,IAC1DA,OAAO,CAACG,aAAa,IAAIH,OAAO,CAACG,aAAa,CAACC,WACnD,CAAC;EACL;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,SAASA,CAACC,KAAK,EAAE;;IAE7B,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,KAAK,CAAC,CAAC,IAAIG,QAAQ,CAACH,KAAK,CAAC,IAAII,IAAI,CAACC,KAAK,CAACL,KAAK,CAAC,IAAIA,KAAK;EACrF;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO,SAASM,UAAUA,CAACC,GAAG,EAAE;IAC5B,OAAQ,mHAAmH,CAAEC,IAAI,CAACD,GAAG,CAAC;EAC1I;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO,SAASE,YAAYA,CAACF,GAAG,EAAE;IAC9B,IAAMG,IAAI,GAAG,+FAA+F;IAC5G,OAAOA,IAAI,CAACF,IAAI,CAACD,GAAG,CAAC;EACzB;EAEO,SAASI,eAAeA,CAACJ,GAAG,EAAE;IACjC,IAAMK,KAAK,GAAG,CAACL,GAAG,IAAI,EAAE,EAAEK,KAAK,CAAC,gCAAgC,CAAC;IACjE,IAAMC,MAAM,GAAG,CAAED,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAK,EAAE,EAAEE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IACjE,IAAMC,aAAa,GAAG,CAClB,aAAa,EACb,aAAa,EACb,aAAa,CAChB;IAED,SAAAC,EAAA,MAAAC,cAAA,GAA2BF,aAAa,EAAAC,EAAA,GAAAC,cAAA,CAAAC,MAAA,EAAAF,EAAA,IAAE;MAArC,IAAMG,YAAY,GAAAF,cAAA,CAAAD,EAAA;MACnB,IAAIH,MAAM,CAACO,QAAQ,CAACD,YAAY,CAAC,EAAE;QAC/B,OAAON,MAAM;;;IAIrB,OAAO,WAAW;EACtB;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASQ,WAAWA,GAAwB;IAAA,IAAvBC,gBAAgB,GAAAC,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;IAC7C,IAAME,EAAE,GAAGH,gBAAgB,CAACG,EAAE;IAC9B,IAAMlB,GAAG,GAAGe,gBAAgB,CAACf,GAAG;IAChC,IAAMmB,OAAO,GAAGD,EAAE,IAAIlB,GAAG;IAEzB,IAAI,CAACmB,OAAO,EAAE;MACV,MAAM,IAAIC,KAAK,CAAC,6GAA6G,CAAC;;IAGlI,IAAI5B,SAAS,CAAC2B,OAAO,CAAC,EAAE;MACpB,4BAAApC,MAAA,CAA4BoC,OAAO;;IAGvC,IAAIpB,UAAU,CAACoB,OAAO,CAAC,EAAE;MACrB,OAAOA,OAAO,CAACZ,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;;IAG7C,IAAIW,EAAE,EAAE;MACJ,MAAM,IAAIG,SAAS,UAAAtC,MAAA,CAAKmC,EAAE,oCAA4B,CAAC;;IAG3D,MAAM,IAAIG,SAAS,UAAAtC,MAAA,CAAKoC,OAAO,mCAA2B,CAAC;EAC/D;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,IAAMG,SAAS,GAAG,SAAZA,SAASA,CAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAmE;IAAA,IAAjEC,MAAM,GAAAV,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,kBAAkB;IAAA,IAAEW,OAAO,GAAAX,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,qBAAqB;IAC/G,IAAMY,UAAU,GAAG,OAAOJ,SAAS,KAAK,QAAQ,GAAG,CAACA,SAAS,CAAC,GAAGA,SAAS;IAE1EI,UAAU,CAACC,OAAO,CAAC,UAACC,MAAM,EAAK;MAC3BP,MAAM,CAACG,MAAM,CAAC,CAACI,MAAM,EAAEL,QAAQ,CAAC;KACnC,CAAC;IAEF,OAAO;MACHM,MAAM,EAAE,SAAAA;QAAA,OAAMH,UAAU,CAACC,OAAO,CAAC,UAACC,MAAM;UAAA,OAAKP,MAAM,CAACI,OAAO,CAAC,CAACG,MAAM,EAAEL,QAAQ,CAAC;UAAC;;KAClF;EACL,CAAC;EAEM,IAAMO,aAAa,GAAG,SAAhBA,aAAaA,GAAS;IAC/BC,OAAO,CAACC,GAAG,CACP,6GAA6G,EAC7G,gCAAgC,EAChC,6BACJ,CAAC;EACL,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,wBAAwBA,CAACC,YAAY,EAAkB;IAAA,IAAhBC,GAAG,GAAArB,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGsB,QAAQ;IACjE,IAAI,CAACF,YAAY,IAAI,CAACC,GAAG,IAAI,OAAOA,GAAG,CAACE,gBAAgB,KAAK,UAAU,EAAE;MACrE,OAAO,IAAI;;IAGf,IAAMC,OAAO,GAAGH,GAAG,CAACE,gBAAgB,CAAC,QAAQ,CAAC;IAE9C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,CAAC7B,MAAM,EAAE8B,CAAC,EAAE,EAAE;MACrC,IAAID,OAAO,CAACC,CAAC,CAAC,IAAID,OAAO,CAACC,CAAC,CAAC,CAACC,aAAa,KAAKN,YAAY,EAAE;QACzD,OAAOI,OAAO,CAACC,CAAC,CAAC;;;IAIzB,OAAO,IAAI;EACf;;ECjMA,IAAME,mBAAmB,GAAG,OAAOC,KAAK,CAACC,SAAS,CAAChE,OAAO,KAAK,WAAW;EAC1E,IAAMiE,kBAAkB,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,WAAW,KAAK,WAAW;EAErG,IAAI,CAACvE,eAAe,KAAK,CAACkE,mBAAmB,IAAI,CAACG,kBAAkB,CAAC,EAAE;IACnE,MAAM,IAAI1B,KAAK,CAAC,+DAA+D,CAAC;EACpF;;;;;;;;ECPA;EACA;EACA;EACA;EACA;EACA;;EAEA,CAAC,UAAS6B,IAAI,EAAE;;IAGd,IAAIA,IAAI,CAACC,OAAO,EAAE;MAChB;;IAGF,IAAIC,cAAc,GAAGC,MAAM,CAACP,SAAS,CAACM,cAAc;IACpD,IAAIE,SAAS,GAAGD,MAAM,CAACE,cAAc,IAAK,YAAW;MACnD,IAAI;;QAEF,OAAOF,MAAM,CAACE,cAAc,CAAC,EAAE,EAAE,GAAG,EAAE;UAAE7D,KAAK,EAAE;SAAG,CAAC,CAAC8D,CAAC,KAAK,CAAC;OAC5D,CAAC,OAAOC,CAAC,EAAE;KACb,EAAG;IAEJ,IAAIF,cAAc,GAAG,UAASG,MAAM,EAAEC,IAAI,EAAEjE,KAAK,EAAE;MACjD,IAAI4D,SAAS,EAAE;QACbD,MAAM,CAACE,cAAc,CAACG,MAAM,EAAEC,IAAI,EAAE;UAClCC,YAAY,EAAE,IAAI;UAClBC,QAAQ,EAAE,IAAI;UACdnE,KAAK,EAAEA;SACR,CAAC;OACH,MAAM;QACLgE,MAAM,CAACC,IAAI,CAAC,GAAGjE,KAAK;;KAEvB;IAEDwD,IAAI,CAACC,OAAO,GAAI,YAAW;;MAGzB,SAASA,OAAOA,GAAG;QACjB,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE;UACnB,MAAM,IAAI7B,SAAS,CAAC,oCAAoC,CAAC;;QAG3DiC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAEO,KAAK,CAAC,UAAU,CAAC,CAAC;;;QAG9C,IAAI7C,SAAS,CAACL,MAAM,GAAG,CAAC,EAAE;;UAExB,MAAM,IAAIU,SAAS,CAAC,mCAAmC,CAAC;;;;;MAK5DiC,cAAc,CAACJ,OAAO,CAACL,SAAS,EAAE,QAAQ,EAAE,UAASiB,GAAG,EAAE;QACxDC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC;QAE7B,IAAI,CAACC,QAAQ,CAACF,GAAG,CAAC,EAAE;UAClB,OAAO,KAAK;;QAGd,IAAIG,KAAK,GAAGH,GAAG,CAAC,IAAI,CAACI,GAAG,CAAC;QACzB,IAAID,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKH,GAAG,EAAE;UAC7B,OAAOA,GAAG,CAAC,IAAI,CAACI,GAAG,CAAC;UACpB,OAAO,IAAI;;QAGb,OAAO,KAAK;OACb,CAAC;;;MAGFZ,cAAc,CAACJ,OAAO,CAACL,SAAS,EAAE,KAAK,EAAE,UAASiB,GAAG,EAAE;QACrDC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC;QAE1B,IAAI,CAACC,QAAQ,CAACF,GAAG,CAAC,EAAE;UAClB,OAAO,KAAK,CAAC;;QAGf,IAAIG,KAAK,GAAGH,GAAG,CAAC,IAAI,CAACI,GAAG,CAAC;QACzB,IAAID,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKH,GAAG,EAAE;UAC7B,OAAOG,KAAK,CAAC,CAAC,CAAC;;QAGjB,OAAO,KAAK,CAAC;OACd,CAAC;;;MAGFX,cAAc,CAACJ,OAAO,CAACL,SAAS,EAAE,KAAK,EAAE,UAASiB,GAAG,EAAE;QACrDC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC;QAE1B,IAAI,CAACC,QAAQ,CAACF,GAAG,CAAC,EAAE;UAClB,OAAO,KAAK;;QAGd,IAAIG,KAAK,GAAGH,GAAG,CAAC,IAAI,CAACI,GAAG,CAAC;QACzB,IAAID,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKH,GAAG,EAAE;UAC7B,OAAO,IAAI;;QAGb,OAAO,KAAK;OACb,CAAC;;;MAGFR,cAAc,CAACJ,OAAO,CAACL,SAAS,EAAE,KAAK,EAAE,UAASiB,GAAG,EAAErE,KAAK,EAAE;QAC5DsE,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC;QAE1B,IAAI,CAACC,QAAQ,CAACF,GAAG,CAAC,EAAE;UAClB,MAAM,IAAIzC,SAAS,CAAC,oCAAoC,CAAC;;QAG3D,IAAI4C,KAAK,GAAGH,GAAG,CAAC,IAAI,CAACI,GAAG,CAAC;QACzB,IAAID,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKH,GAAG,EAAE;UAC7BG,KAAK,CAAC,CAAC,CAAC,GAAGxE,KAAK;UAChB,OAAO,IAAI;;QAGb6D,cAAc,CAACQ,GAAG,EAAE,IAAI,CAACI,GAAG,EAAE,CAACJ,GAAG,EAAErE,KAAK,CAAC,CAAC;QAC3C,OAAO,IAAI;OACZ,CAAC;MAEF,SAASsE,aAAaA,CAACR,CAAC,EAAEY,UAAU,EAAE;QACpC,IAAI,CAACH,QAAQ,CAACT,CAAC,CAAC,IAAI,CAACJ,cAAc,CAAC/E,IAAI,CAACmF,CAAC,EAAE,KAAK,CAAC,EAAE;UAClD,MAAM,IAAIlC,SAAS,CACjB8C,UAAU,GAAG,0CAA0C,GACvD,OAAOZ,CACT,CAAC;;;MAIL,SAASM,KAAKA,CAACO,MAAM,EAAE;QACrB,OAAOA,MAAM,GAAG,GAAG,GAAGC,IAAI,EAAE,GAAG,GAAG,GAAGA,IAAI,EAAE;;MAG7C,SAASA,IAAIA,GAAG;QACd,OAAOxE,IAAI,CAACyE,MAAM,EAAE,CAACnG,QAAQ,EAAE,CAACoG,SAAS,CAAC,CAAC,CAAC;;MAG9CjB,cAAc,CAACJ,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC;MAC1C,OAAOA,OAAO;KACf,EAAG;IAEJ,SAASc,QAAQA,CAACT,CAAC,EAAE;MACnB,OAAOH,MAAM,CAACG,CAAC,CAAC,KAAKA,CAAC;;EAG1B,CAAC,EACC,OAAOiB,UAAU,KAAK,WAAW,GAAGA,UAAU,GAC9C,OAAOvB,IAAI,KAAK,WAAW,GAAGA,IAAI,GAClC,OAAOF,MAAM,KAAK,WAAW,GAAGA,MAAM,GACtC,OAAO7E,cAAM,KAAK,WAAW,GAAGA,cAAM,GAAGuG,cAC3C,CAAC;;;ECpJD;EACA;EACA;EACA;;EAEA,CAAC,SAASC,GAAGA,CAAChB,IAAI,EAACiB,OAAO,EAACC,UAAU,EAAC;;IAErCD,OAAO,CAACjB,IAAI,CAAC,GAAGiB,OAAO,CAACjB,IAAI,CAAC,IAAIkB,UAAU,EAAE;IAC7C,IAAoCC,MAAM,CAACC,OAAO,EAAE;MAAED,cAAc,GAAGF,OAAO,CAACjB,IAAI,CAAC;;EAErF,CAAC,EAAE,SAAS,EAAC,OAAOxF,cAAM,IAAI,WAAW,GAAGA,cAAM,GAAGuG,cAAI,EAAC,SAASM,GAAGA,GAAE;;IAIvE,IAAIC,WAAW;MAAEC,KAAK;MAAEC,gBAAgB;MACvCC,QAAQ,GAAG/B,MAAM,CAACP,SAAS,CAAC1E,QAAQ;MACpCiH,KAAK,GAAI,OAAOC,YAAY,IAAI,WAAW,GAC1C,SAASD,KAAKA,CAACE,EAAE,EAAE;QAAE,OAAOD,YAAY,CAACC,EAAE,CAAC;OAAG,GAC/CC,UAAU;;;IAIZ,IAAI;MACHnC,MAAM,CAACE,cAAc,CAAC,EAAE,EAAC,GAAG,EAAC,EAAE,CAAC;MAChC0B,WAAW,GAAG,SAASA,WAAWA,CAACQ,GAAG,EAAC9B,IAAI,EAAC+B,GAAG,EAACC,MAAM,EAAE;QACvD,OAAOtC,MAAM,CAACE,cAAc,CAACkC,GAAG,EAAC9B,IAAI,EAAC;UACrCjE,KAAK,EAAEgG,GAAG;UACV7B,QAAQ,EAAE,IAAI;UACdD,YAAY,EAAE+B,MAAM,KAAK;SACzB,CAAC;OACF;KACD,CACD,OAAOC,GAAG,EAAE;MACXX,WAAW,GAAG,SAASA,WAAWA,CAACQ,GAAG,EAAC9B,IAAI,EAAC+B,GAAG,EAAE;QAChDD,GAAG,CAAC9B,IAAI,CAAC,GAAG+B,GAAG;QACf,OAAOD,GAAG;OACV;;;;IAIFN,gBAAgB,GAAI,SAASU,KAAKA,GAAG;MACpC,IAAIC,KAAK,EAAEC,IAAI,EAAEC,IAAI;MAErB,SAASC,IAAIA,CAACV,EAAE,EAACrC,IAAI,EAAE;QACtB,IAAI,CAACqC,EAAE,GAAGA,EAAE;QACZ,IAAI,CAACrC,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACgD,IAAI,GAAG,KAAK,CAAC;;MAGnB,OAAO;QACNC,GAAG,EAAE,SAASA,GAAGA,CAACZ,EAAE,EAACrC,IAAI,EAAE;UAC1B8C,IAAI,GAAG,IAAIC,IAAI,CAACV,EAAE,EAACrC,IAAI,CAAC;UACxB,IAAI6C,IAAI,EAAE;YACTA,IAAI,CAACG,IAAI,GAAGF,IAAI;WAChB,MACI;YACJF,KAAK,GAAGE,IAAI;;UAEbD,IAAI,GAAGC,IAAI;UACXA,IAAI,GAAG,KAAK,CAAC;SACb;QACDI,KAAK,EAAE,SAASA,KAAKA,GAAG;UACvB,IAAIC,CAAC,GAAGP,KAAK;UACbA,KAAK,GAAGC,IAAI,GAAGb,KAAK,GAAG,KAAK,CAAC;UAE7B,OAAOmB,CAAC,EAAE;YACTA,CAAC,CAACd,EAAE,CAAClH,IAAI,CAACgI,CAAC,CAACnD,IAAI,CAAC;YACjBmD,CAAC,GAAGA,CAAC,CAACH,IAAI;;;OAGZ;KACD,EAAG;IAEJ,SAASI,QAAQA,CAACf,EAAE,EAACrC,IAAI,EAAE;MAC1BiC,gBAAgB,CAACgB,GAAG,CAACZ,EAAE,EAACrC,IAAI,CAAC;MAC7B,IAAI,CAACgC,KAAK,EAAE;QACXA,KAAK,GAAGG,KAAK,CAACF,gBAAgB,CAACiB,KAAK,CAAC;;;;;IAKvC,SAASG,UAAUA,CAACC,CAAC,EAAE;MACtB,IAAIC,KAAK;QAAEC,MAAM,GAAG,OAAOF,CAAC;MAE5B,IAAIA,CAAC,IAAI,IAAI,KAEXE,MAAM,IAAI,QAAQ,IAAIA,MAAM,IAAI,UAAU,CAC1C,EACA;QACDD,KAAK,GAAGD,CAAC,CAACG,IAAI;;MAEf,OAAO,OAAOF,KAAK,IAAI,UAAU,GAAGA,KAAK,GAAG,KAAK;;IAGlD,SAASG,MAAMA,GAAG;MACjB,KAAK,IAAIlE,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAC,IAAI,CAACmE,KAAK,CAACjG,MAAM,EAAE8B,CAAC,EAAE,EAAE;QACvCoE,cAAc,CACb,IAAI,EACH,IAAI,CAACC,KAAK,KAAK,CAAC,GAAI,IAAI,CAACF,KAAK,CAACnE,CAAC,CAAC,CAACsE,OAAO,GAAG,IAAI,CAACH,KAAK,CAACnE,CAAC,CAAC,CAACuE,OAAO,EAClE,IAAI,CAACJ,KAAK,CAACnE,CAAC,CACb,CAAC;;MAEF,IAAI,CAACmE,KAAK,CAACjG,MAAM,GAAG,CAAC;;;;;;IAMtB,SAASkG,cAAcA,CAAC5D,IAAI,EAACgE,EAAE,EAACL,KAAK,EAAE;MACtC,IAAIM,GAAG,EAAEV,KAAK;MACd,IAAI;QACH,IAAIS,EAAE,KAAK,KAAK,EAAE;UACjBL,KAAK,CAACO,MAAM,CAAClE,IAAI,CAACmE,GAAG,CAAC;SACtB,MACI;UACJ,IAAIH,EAAE,KAAK,IAAI,EAAE;YAChBC,GAAG,GAAGjE,IAAI,CAACmE,GAAG;WACd,MACI;YACJF,GAAG,GAAGD,EAAE,CAAC7I,IAAI,CAAC,KAAK,CAAC,EAAC6E,IAAI,CAACmE,GAAG,CAAC;;UAG/B,IAAIF,GAAG,KAAKN,KAAK,CAACS,OAAO,EAAE;YAC1BT,KAAK,CAACO,MAAM,CAAC9F,SAAS,CAAC,qBAAqB,CAAC,CAAC;WAC9C,MACI,IAAImF,KAAK,GAAGF,UAAU,CAACY,GAAG,CAAC,EAAE;YACjCV,KAAK,CAACpI,IAAI,CAAC8I,GAAG,EAACN,KAAK,CAACU,OAAO,EAACV,KAAK,CAACO,MAAM,CAAC;WAC1C,MACI;YACJP,KAAK,CAACU,OAAO,CAACJ,GAAG,CAAC;;;OAGpB,CACD,OAAOvB,GAAG,EAAE;QACXiB,KAAK,CAACO,MAAM,CAACxB,GAAG,CAAC;;;IAInB,SAAS2B,OAAOA,CAACF,GAAG,EAAE;MACrB,IAAIZ,KAAK;QAAEvD,IAAI,GAAG,IAAI;;;MAGtB,IAAIA,IAAI,CAACsE,SAAS,EAAE;QAAE;;MAEtBtE,IAAI,CAACsE,SAAS,GAAG,IAAI;;;MAGrB,IAAItE,IAAI,CAACuE,GAAG,EAAE;QACbvE,IAAI,GAAGA,IAAI,CAACuE,GAAG;;MAGhB,IAAI;QACH,IAAIhB,KAAK,GAAGF,UAAU,CAACc,GAAG,CAAC,EAAE;UAC5Bf,QAAQ,CAAC,YAAU;YAClB,IAAIoB,WAAW,GAAG,IAAIC,cAAc,CAACzE,IAAI,CAAC;YAC1C,IAAI;cACHuD,KAAK,CAACpI,IAAI,CAACgJ,GAAG,EACb,SAASO,SAASA,GAAE;gBAAEL,OAAO,CAACM,KAAK,CAACH,WAAW,EAACzG,SAAS,CAAC;eAAG,EAC7D,SAAS6G,QAAQA,GAAE;gBAAEV,MAAM,CAACS,KAAK,CAACH,WAAW,EAACzG,SAAS,CAAC;eACzD,CAAC;aACD,CACD,OAAO2E,GAAG,EAAE;cACXwB,MAAM,CAAC/I,IAAI,CAACqJ,WAAW,EAAC9B,GAAG,CAAC;;WAE7B,CAAC;SACF,MACI;UACJ1C,IAAI,CAACmE,GAAG,GAAGA,GAAG;UACdnE,IAAI,CAAC6D,KAAK,GAAG,CAAC;UACd,IAAI7D,IAAI,CAAC2D,KAAK,CAACjG,MAAM,GAAG,CAAC,EAAE;YAC1B0F,QAAQ,CAACM,MAAM,EAAC1D,IAAI,CAAC;;;OAGvB,CACD,OAAO0C,GAAG,EAAE;QACXwB,MAAM,CAAC/I,IAAI,CAAC,IAAIsJ,cAAc,CAACzE,IAAI,CAAC,EAAC0C,GAAG,CAAC;;;IAI3C,SAASwB,MAAMA,CAACC,GAAG,EAAE;MACpB,IAAInE,IAAI,GAAG,IAAI;;;MAGf,IAAIA,IAAI,CAACsE,SAAS,EAAE;QAAE;;MAEtBtE,IAAI,CAACsE,SAAS,GAAG,IAAI;;;MAGrB,IAAItE,IAAI,CAACuE,GAAG,EAAE;QACbvE,IAAI,GAAGA,IAAI,CAACuE,GAAG;;MAGhBvE,IAAI,CAACmE,GAAG,GAAGA,GAAG;MACdnE,IAAI,CAAC6D,KAAK,GAAG,CAAC;MACd,IAAI7D,IAAI,CAAC2D,KAAK,CAACjG,MAAM,GAAG,CAAC,EAAE;QAC1B0F,QAAQ,CAACM,MAAM,EAAC1D,IAAI,CAAC;;;IAIvB,SAAS6E,eAAeA,CAACC,WAAW,EAACC,GAAG,EAACC,QAAQ,EAACC,QAAQ,EAAE;MAC3D,KAAK,IAAIC,GAAG,GAAC,CAAC,EAAEA,GAAG,GAACH,GAAG,CAACrH,MAAM,EAAEwH,GAAG,EAAE,EAAE;QACtC,CAAC,SAASC,IAAIA,CAACD,GAAG,EAAC;UAClBJ,WAAW,CAACT,OAAO,CAACU,GAAG,CAACG,GAAG,CAAC,CAAC,CAC5BzB,IAAI,CACJ,SAAS2B,UAAUA,CAACjB,GAAG,EAAC;YACvBa,QAAQ,CAACE,GAAG,EAACf,GAAG,CAAC;WACjB,EACDc,QACD,CAAC;SACD,EAAEC,GAAG,CAAC;;;IAIT,SAAST,cAAcA,CAACzE,IAAI,EAAE;MAC7B,IAAI,CAACuE,GAAG,GAAGvE,IAAI;MACf,IAAI,CAACsE,SAAS,GAAG,KAAK;;IAGvB,SAASe,OAAOA,CAACrF,IAAI,EAAE;MACtB,IAAI,CAACoE,OAAO,GAAGpE,IAAI;MACnB,IAAI,CAAC6D,KAAK,GAAG,CAAC;MACd,IAAI,CAACS,SAAS,GAAG,KAAK;MACtB,IAAI,CAACX,KAAK,GAAG,EAAE;MACf,IAAI,CAACQ,GAAG,GAAG,KAAK,CAAC;;IAGlB,SAASmB,OAAOA,CAACC,QAAQ,EAAE;MAC1B,IAAI,OAAOA,QAAQ,IAAI,UAAU,EAAE;QAClC,MAAMnH,SAAS,CAAC,gBAAgB,CAAC;;MAGlC,IAAI,IAAI,CAACoH,OAAO,KAAK,CAAC,EAAE;QACvB,MAAMpH,SAAS,CAAC,eAAe,CAAC;;;;;MAKjC,IAAI,CAACoH,OAAO,GAAG,CAAC;MAEhB,IAAIjB,GAAG,GAAG,IAAIc,OAAO,CAAC,IAAI,CAAC;MAE3B,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS5B,IAAIA,CAACK,OAAO,EAACC,OAAO,EAAE;QAC7C,IAAIT,CAAC,GAAG;UACPQ,OAAO,EAAE,OAAOA,OAAO,IAAI,UAAU,GAAGA,OAAO,GAAG,IAAI;UACtDC,OAAO,EAAE,OAAOA,OAAO,IAAI,UAAU,GAAGA,OAAO,GAAG;SAClD;;;;QAIDT,CAAC,CAACc,OAAO,GAAG,IAAI,IAAI,CAACqB,WAAW,CAAC,SAASC,YAAYA,CAACrB,OAAO,EAACH,MAAM,EAAE;UACtE,IAAI,OAAOG,OAAO,IAAI,UAAU,IAAI,OAAOH,MAAM,IAAI,UAAU,EAAE;YAChE,MAAM9F,SAAS,CAAC,gBAAgB,CAAC;;UAGlCkF,CAAC,CAACe,OAAO,GAAGA,OAAO;UACnBf,CAAC,CAACY,MAAM,GAAGA,MAAM;SACjB,CAAC;QACFK,GAAG,CAACZ,KAAK,CAACgC,IAAI,CAACrC,CAAC,CAAC;QAEjB,IAAIiB,GAAG,CAACV,KAAK,KAAK,CAAC,EAAE;UACpBT,QAAQ,CAACM,MAAM,EAACa,GAAG,CAAC;;QAGrB,OAAOjB,CAAC,CAACc,OAAO;OAChB;MACD,IAAI,CAAC,OAAO,CAAC,GAAG,SAASwB,OAAOA,CAAC7B,OAAO,EAAE;QACzC,OAAO,IAAI,CAACN,IAAI,CAAC,KAAK,CAAC,EAACM,OAAO,CAAC;OAChC;MAED,IAAI;QACHwB,QAAQ,CAACpK,IAAI,CACZ,KAAK,CAAC,EACN,SAAS0K,aAAaA,CAAC1B,GAAG,EAAC;UAC1BE,OAAO,CAAClJ,IAAI,CAACoJ,GAAG,EAACJ,GAAG,CAAC;SACrB,EACD,SAAS2B,YAAYA,CAAC3B,GAAG,EAAE;UAC1BD,MAAM,CAAC/I,IAAI,CAACoJ,GAAG,EAACJ,GAAG,CAAC;SAEtB,CAAC;OACD,CACD,OAAOzB,GAAG,EAAE;QACXwB,MAAM,CAAC/I,IAAI,CAACoJ,GAAG,EAAC7B,GAAG,CAAC;;;IAItB,IAAIqD,gBAAgB,GAAGhE,WAAW,CAAC,EAAE,EAAC,aAAa,EAACuD,OAAO,mBACzC,KAClB,CAAC;;;IAGDA,OAAO,CAAC1F,SAAS,GAAGmG,gBAAgB;;;IAGpChE,WAAW,CAACgE,gBAAgB,EAAC,SAAS,EAAC,CAAC,mBACtB,KAClB,CAAC;IAEDhE,WAAW,CAACuD,OAAO,EAAC,SAAS,EAAC,SAASU,eAAeA,CAAC7B,GAAG,EAAE;MAC3D,IAAIW,WAAW,GAAG,IAAI;;;;MAItB,IAAIX,GAAG,IAAI,OAAOA,GAAG,IAAI,QAAQ,IAAIA,GAAG,CAACqB,OAAO,KAAK,CAAC,EAAE;QACvD,OAAOrB,GAAG;;MAGX,OAAO,IAAIW,WAAW,CAAC,SAASS,QAAQA,CAAClB,OAAO,EAACH,MAAM,EAAC;QACvD,IAAI,OAAOG,OAAO,IAAI,UAAU,IAAI,OAAOH,MAAM,IAAI,UAAU,EAAE;UAChE,MAAM9F,SAAS,CAAC,gBAAgB,CAAC;;QAGlCiG,OAAO,CAACF,GAAG,CAAC;OACZ,CAAC;KACF,CAAC;IAEFpC,WAAW,CAACuD,OAAO,EAAC,QAAQ,EAAC,SAASW,cAAcA,CAAC9B,GAAG,EAAE;MACzD,OAAO,IAAI,IAAI,CAAC,SAASoB,QAAQA,CAAClB,OAAO,EAACH,MAAM,EAAC;QAChD,IAAI,OAAOG,OAAO,IAAI,UAAU,IAAI,OAAOH,MAAM,IAAI,UAAU,EAAE;UAChE,MAAM9F,SAAS,CAAC,gBAAgB,CAAC;;QAGlC8F,MAAM,CAACC,GAAG,CAAC;OACX,CAAC;KACF,CAAC;IAEFpC,WAAW,CAACuD,OAAO,EAAC,KAAK,EAAC,SAASY,WAAWA,CAACnB,GAAG,EAAE;MACnD,IAAID,WAAW,GAAG,IAAI;;;MAGtB,IAAI5C,QAAQ,CAAC/G,IAAI,CAAC4J,GAAG,CAAC,IAAI,gBAAgB,EAAE;QAC3C,OAAOD,WAAW,CAACZ,MAAM,CAAC9F,SAAS,CAAC,cAAc,CAAC,CAAC;;MAErD,IAAI2G,GAAG,CAACrH,MAAM,KAAK,CAAC,EAAE;QACrB,OAAOoH,WAAW,CAACT,OAAO,CAAC,EAAE,CAAC;;MAG/B,OAAO,IAAIS,WAAW,CAAC,SAASS,QAAQA,CAAClB,OAAO,EAACH,MAAM,EAAC;QACvD,IAAI,OAAOG,OAAO,IAAI,UAAU,IAAI,OAAOH,MAAM,IAAI,UAAU,EAAE;UAChE,MAAM9F,SAAS,CAAC,gBAAgB,CAAC;;QAGlC,IAAI+H,GAAG,GAAGpB,GAAG,CAACrH,MAAM;UAAE0I,IAAI,GAAGzG,KAAK,CAACwG,GAAG,CAAC;UAAEE,KAAK,GAAG,CAAC;QAElDxB,eAAe,CAACC,WAAW,EAACC,GAAG,EAAC,SAASC,QAAQA,CAACE,GAAG,EAACf,GAAG,EAAE;UAC1DiC,IAAI,CAAClB,GAAG,CAAC,GAAGf,GAAG;UACf,IAAI,EAAEkC,KAAK,KAAKF,GAAG,EAAE;YACpB9B,OAAO,CAAC+B,IAAI,CAAC;;SAEd,EAAClC,MAAM,CAAC;OACT,CAAC;KACF,CAAC;IAEFnC,WAAW,CAACuD,OAAO,EAAC,MAAM,EAAC,SAASgB,YAAYA,CAACvB,GAAG,EAAE;MACrD,IAAID,WAAW,GAAG,IAAI;;;MAGtB,IAAI5C,QAAQ,CAAC/G,IAAI,CAAC4J,GAAG,CAAC,IAAI,gBAAgB,EAAE;QAC3C,OAAOD,WAAW,CAACZ,MAAM,CAAC9F,SAAS,CAAC,cAAc,CAAC,CAAC;;MAGrD,OAAO,IAAI0G,WAAW,CAAC,SAASS,QAAQA,CAAClB,OAAO,EAACH,MAAM,EAAC;QACvD,IAAI,OAAOG,OAAO,IAAI,UAAU,IAAI,OAAOH,MAAM,IAAI,UAAU,EAAE;UAChE,MAAM9F,SAAS,CAAC,gBAAgB,CAAC;;QAGlCyG,eAAe,CAACC,WAAW,EAACC,GAAG,EAAC,SAASC,QAAQA,CAACE,GAAG,EAACf,GAAG,EAAC;UACzDE,OAAO,CAACF,GAAG,CAAC;SACZ,EAACD,MAAM,CAAC;OACT,CAAC;KACF,CAAC;IAEF,OAAOoB,OAAO;EACf,CAAC,CAAC;;;ECpXF;EACA;EACA;;EAEO,IAAMiB,WAAW,GAAG,IAAItG,OAAO,EAAE;;EAExC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASuG,aAAaA,CAACC,MAAM,EAAEhG,IAAI,EAAEjC,QAAQ,EAAE;IAClD,IAAMkI,eAAe,GAAGH,WAAW,CAACI,GAAG,CAACF,MAAM,CAACvK,OAAO,CAAC,IAAI,EAAE;IAE7D,IAAI,EAAEuE,IAAI,IAAIiG,eAAe,CAAC,EAAE;MAC5BA,eAAe,CAACjG,IAAI,CAAC,GAAG,EAAE;;IAG9BiG,eAAe,CAACjG,IAAI,CAAC,CAACkF,IAAI,CAACnH,QAAQ,CAAC;IACpC+H,WAAW,CAACK,GAAG,CAACH,MAAM,CAACvK,OAAO,EAAEwK,eAAe,CAAC;EACpD;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASG,YAAYA,CAACJ,MAAM,EAAEhG,IAAI,EAAE;IACvC,IAAMiG,eAAe,GAAGH,WAAW,CAACI,GAAG,CAACF,MAAM,CAACvK,OAAO,CAAC,IAAI,EAAE;IAC7D,OAAOwK,eAAe,CAACjG,IAAI,CAAC,IAAI,EAAE;EACtC;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASqG,cAAcA,CAACL,MAAM,EAAEhG,IAAI,EAAEjC,QAAQ,EAAE;IACnD,IAAMkI,eAAe,GAAGH,WAAW,CAACI,GAAG,CAACF,MAAM,CAACvK,OAAO,CAAC,IAAI,EAAE;IAE7D,IAAI,CAACwK,eAAe,CAACjG,IAAI,CAAC,EAAE;MACxB,OAAO,IAAI;;;;IAIf,IAAI,CAACjC,QAAQ,EAAE;MACXkI,eAAe,CAACjG,IAAI,CAAC,GAAG,EAAE;MAC1B8F,WAAW,CAACK,GAAG,CAACH,MAAM,CAACvK,OAAO,EAAEwK,eAAe,CAAC;MAEhD,OAAO,IAAI;;IAGf,IAAMK,KAAK,GAAGL,eAAe,CAACjG,IAAI,CAAC,CAAC7E,OAAO,CAAC4C,QAAQ,CAAC;IAErD,IAAIuI,KAAK,KAAK,CAAC,CAAC,EAAE;MACdL,eAAe,CAACjG,IAAI,CAAC,CAACuG,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;;IAG1CR,WAAW,CAACK,GAAG,CAACH,MAAM,CAACvK,OAAO,EAAEwK,eAAe,CAAC;IAChD,OAAOA,eAAe,CAACjG,IAAI,CAAC,IAAIiG,eAAe,CAACjG,IAAI,CAAC,CAAC/C,MAAM,KAAK,CAAC;EACtE;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASuJ,cAAcA,CAACR,MAAM,EAAEhG,IAAI,EAAE;IACzC,IAAMiG,eAAe,GAAGG,YAAY,CAACJ,MAAM,EAAEhG,IAAI,CAAC;IAElD,IAAIiG,eAAe,CAAChJ,MAAM,GAAG,CAAC,EAAE;MAC5B,OAAO,KAAK;;IAGhB,IAAMc,QAAQ,GAAGkI,eAAe,CAACQ,KAAK,EAAE;IACxCJ,cAAc,CAACL,MAAM,EAAEhG,IAAI,EAAEjC,QAAQ,CAAC;IACtC,OAAOA,QAAQ;EACnB;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS2I,aAAaA,CAACC,UAAU,EAAEC,UAAU,EAAE;IAClD,IAAMX,eAAe,GAAGH,WAAW,CAACI,GAAG,CAACS,UAAU,CAAC;IAEnDb,WAAW,CAACK,GAAG,CAACS,UAAU,EAAEX,eAAe,CAAC;IAC5CH,WAAW,CAACe,MAAM,CAACF,UAAU,CAAC;EAClC;;ECtGA;EACA;EACA;;EAIA;EACA;EACA;EACA;EACA;EACA;EACO,SAASG,gBAAgBA,CAACC,IAAI,EAAE;IACnC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI;QACAA,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC;OAC1B,CACD,OAAOG,KAAK,EAAE;;QAEV3I,OAAO,CAAC4I,IAAI,CAACD,KAAK,CAAC;QACnB,OAAO,EAAE;;;IAIjB,OAAOH,IAAI;EACf;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASzH,WAAWA,CAAC0G,MAAM,EAAEoB,MAAM,EAAEC,MAAM,EAAE;IAChD,IAAI,CAACrB,MAAM,CAACvK,OAAO,CAACuD,aAAa,IAAI,CAACgH,MAAM,CAACvK,OAAO,CAACuD,aAAa,CAACM,WAAW,EAAE;MAC5E;;IAGJ,IAAIgI,OAAO,GAAG;MACVF,MAAM,EAANA;KACH;IAED,IAAIC,MAAM,KAAK9J,SAAS,EAAE;MACtB+J,OAAO,CAACvL,KAAK,GAAGsL,MAAM;;;;IAI1B,IAAME,SAAS,GAAGtL,UAAU,CAACuL,SAAS,CAACC,SAAS,CAACrM,WAAW,EAAE,CAACyB,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;IACjG,IAAI0K,SAAS,IAAI,CAAC,IAAIA,SAAS,GAAG,EAAE,EAAE;MAClCD,OAAO,GAAGN,IAAI,CAACU,SAAS,CAACJ,OAAO,CAAC;;IAGrCtB,MAAM,CAACvK,OAAO,CAACuD,aAAa,CAACM,WAAW,CAACgI,OAAO,EAAEtB,MAAM,CAAC2B,MAAM,CAAC;EACpE;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,WAAWA,CAAC5B,MAAM,EAAEe,IAAI,EAAE;IACtCA,IAAI,GAAGD,gBAAgB,CAACC,IAAI,CAAC;IAC7B,IAAIc,SAAS,GAAG,EAAE;IAClB,IAAIC,KAAK;IAET,IAAIf,IAAI,CAACgB,KAAK,EAAE;MACZ,IAAIhB,IAAI,CAACgB,KAAK,KAAK,OAAO,EAAE;QACxB,IAAMC,QAAQ,GAAG5B,YAAY,CAACJ,MAAM,EAAEe,IAAI,CAACA,IAAI,CAACK,MAAM,CAAC;QAEvDY,QAAQ,CAAC7J,OAAO,CAAC,UAACwF,OAAO,EAAK;UAC1B,IAAMuD,KAAK,GAAG,IAAIxJ,KAAK,CAACqJ,IAAI,CAACA,IAAI,CAACO,OAAO,CAAC;UAC1CJ,KAAK,CAAClH,IAAI,GAAG+G,IAAI,CAACA,IAAI,CAAC/G,IAAI;UAE3B2D,OAAO,CAACF,MAAM,CAACyD,KAAK,CAAC;UACrBb,cAAc,CAACL,MAAM,EAAEe,IAAI,CAACA,IAAI,CAACK,MAAM,EAAEzD,OAAO,CAAC;SACpD,CAAC;;MAGNkE,SAAS,GAAGzB,YAAY,CAACJ,MAAM,WAAA3K,MAAA,CAAW0L,IAAI,CAACgB,KAAK,CAAE,CAAC;MACvDD,KAAK,GAAGf,IAAI,CAACA,IAAI;KACpB,MACI,IAAIA,IAAI,CAACK,MAAM,EAAE;MAClB,IAAMrJ,QAAQ,GAAGyI,cAAc,CAACR,MAAM,EAAEe,IAAI,CAACK,MAAM,CAAC;MAEpD,IAAIrJ,QAAQ,EAAE;QACV8J,SAAS,CAAC3C,IAAI,CAACnH,QAAQ,CAAC;QACxB+J,KAAK,GAAGf,IAAI,CAAChL,KAAK;;;IAI1B8L,SAAS,CAAC1J,OAAO,CAAC,UAACJ,QAAQ,EAAK;MAC5B,IAAI;QACA,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UAChCA,QAAQ,CAACrD,IAAI,CAACsL,MAAM,EAAE8B,KAAK,CAAC;UAC5B;;QAGJ/J,QAAQ,CAAC6F,OAAO,CAACkE,KAAK,CAAC;OAC1B,CACD,OAAOhI,CAAC,EAAE;;;KAGb,CAAC;EACN;;EC3GA;EACA;EACA;EAMA,IAAMzC,gBAAgB,GAAG,CACrB,SAAS,EACT,cAAc,EACd,YAAY,EACZ,WAAW,EACX,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,UAAU,EACV,YAAY,EACZ,OAAO,EACP,QAAQ,EACR,UAAU,EACV,KAAK,EACL,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,iBAAiB,EACjB,oBAAoB,EACpB,UAAU,EACV,MAAM,EACN,WAAW,EACX,aAAa,EACb,UAAU,EACV,aAAa,EACb,OAAO,EACP,sBAAsB,EACtB,aAAa,EACb,UAAU,EACV,SAAS,EACT,cAAc,EACd,SAAS,EACT,kBAAkB,EAClB,YAAY,EACZ,kBAAkB,EAClB,OAAO,EACP,YAAY,EACZ,WAAW,EACX,cAAc,EACd,OAAO,EACP,YAAY,EACZ,aAAa,EACb,eAAe,EACf,KAAK,EACL,YAAY,EACZ,QAAQ,EACR,kBAAkB,EAClB,OAAO,CACV;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS4K,mBAAmBA,CAACxM,OAAO,EAAiB;IAAA,IAAfyM,QAAQ,GAAA5K,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;IACtD,OAAOD,gBAAgB,CAAC8K,MAAM,CAAC,UAACd,MAAM,EAAES,KAAK,EAAK;MAC9C,IAAM/L,KAAK,GAAGN,OAAO,CAAC2M,YAAY,eAAA/M,MAAA,CAAeyM,KAAK,CAAE,CAAC;MAEzD,IAAI/L,KAAK,IAAIA,KAAK,KAAK,EAAE,EAAE;QACvBsL,MAAM,CAACS,KAAK,CAAC,GAAG/L,KAAK,KAAK,EAAE,GAAG,CAAC,GAAGA,KAAK;;MAG5C,OAAOsL,MAAM;KAChB,EAAEa,QAAQ,CAAC;EAChB;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASG,WAAWA,CAAAC,IAAA,EAAW7M,OAAO,EAAE;IAAA,IAAjB8M,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAC9B,IAAI,CAAC9M,OAAO,EAAE;MACV,MAAM,IAAIkC,SAAS,CAAC,6BAA6B,CAAC;;IAGtD,IAAIlC,OAAO,CAAC2M,YAAY,CAAC,wBAAwB,CAAC,KAAK,IAAI,EAAE;MACzD,OAAO3M,OAAO,CAAC+M,aAAa,CAAC,QAAQ,CAAC;;IAG1C,IAAMC,GAAG,GAAG7J,QAAQ,CAAC8J,aAAa,CAAC,KAAK,CAAC;IACzCD,GAAG,CAACE,SAAS,GAAGJ,IAAI;IAEpB9M,OAAO,CAACmN,WAAW,CAACH,GAAG,CAACI,UAAU,CAAC;IACnCpN,OAAO,CAACqN,YAAY,CAAC,wBAAwB,EAAE,MAAM,CAAC;IAEtD,OAAOrN,OAAO,CAAC+M,aAAa,CAAC,QAAQ,CAAC;EAC1C;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASO,aAAaA,CAACC,QAAQ,EAAwB;IAAA,IAAtB3B,MAAM,GAAA/J,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;IAAA,IAAE7B,OAAO,GAAA6B,SAAA,CAAAL,MAAA,OAAAK,SAAA,MAAAC,SAAA;IACxD,OAAO,IAAIsH,OAAO,CAAC,UAACjB,OAAO,EAAEH,MAAM,EAAK;MACpC,IAAI,CAACpH,UAAU,CAAC2M,QAAQ,CAAC,EAAE;QACvB,MAAM,IAAIrL,SAAS,UAAAtC,MAAA,CAAK2N,QAAQ,mCAA2B,CAAC;;MAGhE,IAAMpM,MAAM,GAAGF,eAAe,CAACsM,QAAQ,CAAC;MACxC,IAAI1M,GAAG,cAAAjB,MAAA,CAAcuB,MAAM,2BAAAvB,MAAA,CAAwB4N,kBAAkB,CAACD,QAAQ,CAAC,CAAE;MAEjF,KAAK,IAAMlB,KAAK,IAAIT,MAAM,EAAE;QACxB,IAAIA,MAAM,CAAC5H,cAAc,CAACqI,KAAK,CAAC,EAAE;UAC9BxL,GAAG,QAAAjB,MAAA,CAAQyM,KAAK,OAAAzM,MAAA,CAAI4N,kBAAkB,CAAC5B,MAAM,CAACS,KAAK,CAAC,CAAC,CAAE;;;MAI/D,IAAMoB,GAAG,GAAG,gBAAgB,IAAI7J,MAAM,GAAG,IAAI8J,cAAc,EAAE,GAAG,IAAIC,cAAc,EAAE;MACpFF,GAAG,CAACG,IAAI,CAAC,KAAK,EAAE/M,GAAG,EAAE,IAAI,CAAC;MAE1B4M,GAAG,CAACI,MAAM,GAAG,YAAW;QACpB,IAAIJ,GAAG,CAACK,MAAM,KAAK,GAAG,EAAE;UACpB9F,MAAM,CAAC,IAAI/F,KAAK,UAAArC,MAAA,CAAK2N,QAAQ,0BAAkB,CAAC,CAAC;UACjD;;QAGJ,IAAIE,GAAG,CAACK,MAAM,KAAK,GAAG,EAAE;UACpB9F,MAAM,CAAC,IAAI/F,KAAK,UAAArC,MAAA,CAAK2N,QAAQ,8BAAsB,CAAC,CAAC;UACrD;;QAGJ,IAAI;UACA,IAAMQ,IAAI,GAAGxC,IAAI,CAACC,KAAK,CAACiC,GAAG,CAACO,YAAY,CAAC;;UAEzC,IAAID,IAAI,CAACE,kBAAkB,KAAK,GAAG,EAAE;;YAEjCrB,WAAW,CAACmB,IAAI,EAAE/N,OAAO,CAAC;YAC1BgI,MAAM,CAAC,IAAI/F,KAAK,UAAArC,MAAA,CAAK2N,QAAQ,8BAAsB,CAAC,CAAC;YACrD;;UAGJpF,OAAO,CAAC4F,IAAI,CAAC;SAChB,CACD,OAAOtC,KAAK,EAAE;UACVzD,MAAM,CAACyD,KAAK,CAAC;;OAEpB;MAEDgC,GAAG,CAACS,OAAO,GAAG,YAAW;QACrB,IAAMJ,MAAM,GAAGL,GAAG,CAACK,MAAM,QAAAlO,MAAA,CAAQ6N,GAAG,CAACK,MAAM,SAAM,EAAE;QACnD9F,MAAM,CAAC,IAAI/F,KAAK,yDAAArC,MAAA,CAAyDkO,MAAM,MAAG,CAAC,CAAC;OACvF;MAEDL,GAAG,CAACU,IAAI,EAAE;KACb,CAAC;EACN;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,gBAAgBA,GAAoB;IAAA,IAAnBC,MAAM,GAAAxM,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGsB,QAAQ;IAC9C,IAAMmL,QAAQ,GAAG,EAAE,CAACC,KAAK,CAACtP,IAAI,CAACoP,MAAM,CAACjL,gBAAgB,CAAC,mCAAmC,CAAC,CAAC;IAE5F,IAAMoL,WAAW,GAAG,SAAdA,WAAWA,CAAI/C,KAAK,EAAK;MAC3B,IAAI,SAAS,IAAI7H,MAAM,IAAId,OAAO,CAAC2I,KAAK,EAAE;QACtC3I,OAAO,CAAC2I,KAAK,0CAAA7L,MAAA,CAA0C6L,KAAK,CAAE,CAAC;;KAEtE;IAED6C,QAAQ,CAAC5L,OAAO,CAAC,UAAC1C,OAAO,EAAK;MAC1B,IAAI;;QAEA,IAAIA,OAAO,CAAC2M,YAAY,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;UACnD;;QAGJ,IAAMf,MAAM,GAAGY,mBAAmB,CAACxM,OAAO,CAAC;QAC3C,IAAMa,GAAG,GAAGc,WAAW,CAACiK,MAAM,CAAC;QAE/B0B,aAAa,CAACzM,GAAG,EAAE+K,MAAM,EAAE5L,OAAO,CAAC,CAACuH,IAAI,CAAC,UAAC+D,IAAI,EAAK;UAC/C,OAAOsB,WAAW,CAACtB,IAAI,EAAEtL,OAAO,CAAC;SACpC,CAAC,CAACyO,KAAK,CAACD,WAAW,CAAC;OACxB,CACD,OAAO/C,KAAK,EAAE;QACV+C,WAAW,CAAC/C,KAAK,CAAC;;KAEzB,CAAC;EACN;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO,SAASiD,YAAYA,GAAoB;IAAA,IAAnBL,MAAM,GAAAxM,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGsB,QAAQ;;IAE1C,IAAIS,MAAM,CAAC+K,wBAAwB,EAAE;MACjC;;IAEJ/K,MAAM,CAAC+K,wBAAwB,GAAG,IAAI;IAEtC,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAItC,KAAK,EAAK;MACzB,IAAI,CAAC1L,UAAU,CAAC0L,KAAK,CAACJ,MAAM,CAAC,EAAE;QAC3B;;;;MAIJ,IAAI,CAACI,KAAK,CAAChB,IAAI,IAAIgB,KAAK,CAAChB,IAAI,CAACgB,KAAK,KAAK,aAAa,EAAE;QACnD;;MAGJ,IAAMuC,YAAY,GAAGvC,KAAK,CAACwC,MAAM,GAAG9L,wBAAwB,CAACsJ,KAAK,CAACwC,MAAM,EAAET,MAAM,CAAC,GAAG,IAAI;MAEzF,IAAIQ,YAAY,EAAE;;;QAGd,IAAME,KAAK,GAAGF,YAAY,CAACG,aAAa;QACxCD,KAAK,CAACE,KAAK,CAACC,aAAa,MAAAtP,MAAA,CAAM0M,KAAK,CAAChB,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC6D,MAAM,OAAI;;KAEnE;IAEDvL,MAAM,CAACwL,gBAAgB,CAAC,SAAS,EAAER,SAAS,CAAC;EACjD;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO,SAASS,uBAAuBA,GAAoB;IAAA,IAAnBhB,MAAM,GAAAxM,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGsB,QAAQ;;IAErD,IAAIS,MAAM,CAAC0L,wBAAwB,EAAE;MACjC;;IAEJ1L,MAAM,CAAC0L,wBAAwB,GAAG,IAAI;IAEtC,IAAMV,SAAS,GAAG,SAAZA,SAASA,CAAItC,KAAK,EAAK;MACzB,IAAI,CAAC1L,UAAU,CAAC0L,KAAK,CAACJ,MAAM,CAAC,EAAE;QAC3B;;MAGJ,IAAMZ,IAAI,GAAGD,gBAAgB,CAACiB,KAAK,CAAChB,IAAI,CAAC;MACzC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACgB,KAAK,KAAK,OAAO,EAAE;QACjC;;MAGJ,IAAMuC,YAAY,GAAGvC,KAAK,CAACwC,MAAM,GAAG9L,wBAAwB,CAACsJ,KAAK,CAACwC,MAAM,EAAET,MAAM,CAAC,GAAG,IAAI;;;MAGzF,IAAIQ,YAAY,IAAI9N,YAAY,CAAC8N,YAAY,CAACU,GAAG,CAAC,EAAE;QAChD,IAAMhF,MAAM,GAAG,IAAIiF,MAAM,CAACX,YAAY,CAAC;QACvCtE,MAAM,CAACkF,UAAU,CAAC,qBAAqB,EAAE7L,MAAM,CAAC8L,QAAQ,CAACC,IAAI,CAAC;;KAErE;IAED/L,MAAM,CAACwL,gBAAgB,CAAC,SAAS,EAAER,SAAS,CAAC;EACjD;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO,SAASgB,iBAAiBA,GAAoB;IAAA,IAAnBvB,MAAM,GAAAxM,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGsB,QAAQ;;IAE/C,IAAIS,MAAM,CAACiM,wBAAwB,EAAE;MACjC;;IAEJjM,MAAM,CAACiM,wBAAwB,GAAG,IAAI;IAEtC,IAAMrB,WAAW,GAAG,SAAdA,WAAWA,CAAI/C,KAAK,EAAK;MAC3B,IAAI,SAAS,IAAI7H,MAAM,IAAId,OAAO,CAAC2I,KAAK,EAAE;QACtC3I,OAAO,CAAC2I,KAAK,yCAAA7L,MAAA,CAAyC6L,KAAK,CAAE,CAAC;;KAErE;IAED,IAAMmD,SAAS,GAAG,SAAZA,SAASA,CAAItC,KAAK,EAAK;MACzB,IAAI,CAAC1L,UAAU,CAAC0L,KAAK,CAACJ,MAAM,CAAC,EAAE;QAC3B;;MAGJ,IAAMZ,IAAI,GAAGD,gBAAgB,CAACiB,KAAK,CAAChB,IAAI,CAAC;MACzC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACgB,KAAK,KAAK,OAAO,EAAE;QACjC;;MAGJ,IAAMuC,YAAY,GAAGvC,KAAK,CAACwC,MAAM,GAAG9L,wBAAwB,CAACsJ,KAAK,CAACwC,MAAM,EAAET,MAAM,CAAC,GAAG,IAAI;MAEzF,IAAIQ,YAAY,IAAI9N,YAAY,CAAC8N,YAAY,CAACU,GAAG,CAAC,EAAE;QAChD,IAAMhF,MAAM,GAAG,IAAIiF,MAAM,CAACX,YAAY,CAAC;QACvCtE,MAAM,CACDuF,UAAU,EAAE,CACZvI,IAAI,CAAC,UAACwI,OAAO,EAAK;UACf,IAAMC,OAAO,GAAG,IAAIC,MAAM,gBAAArQ,MAAA,CAAgBmQ,OAAO,cAAW,CAAC,CAACG,IAAI,CAACtM,MAAM,CAAC8L,QAAQ,CAACC,IAAI,CAAC;UACxF,IAAIK,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,EAAE;YACvB,IAAMG,GAAG,GAAGC,SAAS,CAACJ,OAAO,CAAC,CAAC,CAAC,CAAC;YACjCzF,MAAM,CAAC8F,cAAc,CAACF,GAAG,CAAC;;UAE9B;SACH,CAAC,CACD1B,KAAK,CAACD,WAAW,CAAC;;KAE9B;IAED5K,MAAM,CAACwL,gBAAgB,CAAC,SAAS,EAAER,SAAS,CAAC;EACjD;;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS0B,eAAeA,GAAG;IAC9B,IAAI1M,MAAM,CAAC2M,qBAAqB,EAAE;MAC9B;;IAEJ3M,MAAM,CAAC2M,qBAAqB,GAAG,IAAI;;;EAGvC;EACA;EACA;IACI,IAAM3B,SAAS,GAAG,SAAZA,SAASA,CAAItC,KAAK,EAAK;MACzB,IAAI,CAAC1L,UAAU,CAAC0L,KAAK,CAACJ,MAAM,CAAC,EAAE;QAC3B;;MAGJ,IAAMZ,IAAI,GAAGD,gBAAgB,CAACiB,KAAK,CAAChB,IAAI,CAAC;MACzC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACgB,KAAK,KAAK,eAAe,EAAE;QACzC;;MAGJ,IAAMuC,YAAY,GAAGvC,KAAK,CAACwC,MAAM,GAAG9L,wBAAwB,CAACsJ,KAAK,CAACwC,MAAM,CAAC,GAAG,IAAI;MAEjF,IAAI,CAACD,YAAY,EAAE;QACf;;MAGJ,IAAM2B,YAAY,GAAG3B,YAAY,CAAClC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;MAC7D,IAAM8D,gBAAgB,GAAGD,YAAY,CAACE,QAAQ,CAAC,iBAAiB,CAAC;MAEjE,IAAI,CAACD,gBAAgB,EAAE;;;;QAInB5B,YAAY,CAACxB,YAAY,CAAC,OAAO,KAAAzN,MAAA,CAAK4Q,YAAY,sBAAmB,CAAC;QACtE,IAAMG,UAAU,GAAG,IAAIC,GAAG,CAAC/B,YAAY,CAAClC,YAAY,CAAC,KAAK,CAAC,CAAC;;;QAG5DgE,UAAU,CAACE,YAAY,CAACnG,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC;QACjDmE,YAAY,CAACxB,YAAY,CAAC,KAAK,EAAEsD,UAAU,CAAC3R,QAAQ,EAAE,CAAC;QACvD;;KAEP;IAED4E,MAAM,CAACwL,gBAAgB,CAAC,SAAS,EAAER,SAAS,CAAC;EACjD;;ECxXA;AACA;EACA;AACA;EACA;AACA;EACA;AACA;EACA;EACA;;EAEO,SAASkC,oBAAoBA,GAAG;IAEnC,IAAM3K,EAAE,GAAI,YAAW;MACnB,IAAIG,GAAG;MAEP,IAAMyK,KAAK,GAAG,CACV,CACI,mBAAmB,EACnB,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACnB,kBAAkB,EAClB,iBAAiB,CACpB;;MAED,CACI,yBAAyB,EACzB,sBAAsB,EACtB,yBAAyB,EACzB,yBAAyB,EACzB,wBAAwB,EACxB,uBAAuB,CAE1B;;MAED,CACI,yBAAyB,EACzB,wBAAwB,EACxB,gCAAgC,EAChC,wBAAwB,EACxB,wBAAwB,EACxB,uBAAuB,CAE1B,EACD,CACI,sBAAsB,EACtB,qBAAqB,EACrB,sBAAsB,EACtB,sBAAsB,EACtB,qBAAqB,EACrB,oBAAoB,CACvB,EACD,CACI,qBAAqB,EACrB,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,mBAAmB,CACtB,CACJ;MAED,IAAIzN,CAAC,GAAG,CAAC;MACT,IAAM0N,CAAC,GAAGD,KAAK,CAACvP,MAAM;MACtB,IAAMuG,GAAG,GAAG,EAAE;MAEd,OAAOzE,CAAC,GAAG0N,CAAC,EAAE1N,CAAC,EAAE,EAAE;QACfgD,GAAG,GAAGyK,KAAK,CAACzN,CAAC,CAAC;QACd,IAAIgD,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAInD,QAAQ,EAAE;UAC3B,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,GAAG,CAAC9E,MAAM,EAAE8B,CAAC,EAAE,EAAE;YAC7ByE,GAAG,CAACgJ,KAAK,CAAC,CAAC,CAAC,CAACzN,CAAC,CAAC,CAAC,GAAGgD,GAAG,CAAChD,CAAC,CAAC;;UAE7B,OAAOyE,GAAG;;;MAIlB,OAAO,KAAK;KACf,EAAG;IAEJ,IAAMkJ,YAAY,GAAG;MACjBC,gBAAgB,EAAE/K,EAAE,CAAC+K,gBAAgB;MACrCC,eAAe,EAAEhL,EAAE,CAACgL;KACvB;IAED,IAAMC,UAAU,GAAG;MACfC,OAAO,WAAAA,QAACrR,OAAO,EAAE;QACb,OAAO,IAAIoJ,OAAO,CAAC,UAACjB,OAAO,EAAEH,MAAM,EAAK;UACpC,IAAMsJ,mBAAmB,GAAG,SAAtBA,mBAAmBA,GAAc;YACnCF,UAAU,CAACG,GAAG,CAAC,kBAAkB,EAAED,mBAAmB,CAAC;YACvDnJ,OAAO,EAAE;WACZ;UAEDiJ,UAAU,CAACI,EAAE,CAAC,kBAAkB,EAAEF,mBAAmB,CAAC;UAEtDtR,OAAO,GAAGA,OAAO,IAAImD,QAAQ,CAACsO,eAAe;UAE7C,IAAMC,aAAa,GAAG1R,OAAO,CAACmG,EAAE,CAACwL,iBAAiB,CAAC,EAAE;UACrD,IAAID,aAAa,YAAYtI,OAAO,EAAE;YAClCsI,aAAa,CAACnK,IAAI,CAAC+J,mBAAmB,CAAC,CAAC7C,KAAK,CAACzG,MAAM,CAAC;;SAE5D,CAAC;OACL;MACD4J,IAAI,WAAAA,OAAG;QACH,OAAO,IAAIxI,OAAO,CAAC,UAACjB,OAAO,EAAEH,MAAM,EAAK;UACpC,IAAI,CAACoJ,UAAU,CAACS,YAAY,EAAE;YAC1B1J,OAAO,EAAE;YACT;;UAGJ,IAAM2J,gBAAgB,GAAG,SAAnBA,gBAAgBA,GAAc;YAChCV,UAAU,CAACG,GAAG,CAAC,kBAAkB,EAAEO,gBAAgB,CAAC;YACpD3J,OAAO,EAAE;WACZ;UAEDiJ,UAAU,CAACI,EAAE,CAAC,kBAAkB,EAAEM,gBAAgB,CAAC;UAEnD,IAAMJ,aAAa,GAAGvO,QAAQ,CAACgD,EAAE,CAAC4L,cAAc,CAAC,EAAE;UACnD,IAAIL,aAAa,YAAYtI,OAAO,EAAE;YAClCsI,aAAa,CAACnK,IAAI,CAACuK,gBAAgB,CAAC,CAACrD,KAAK,CAACzG,MAAM,CAAC;;SAEzD,CAAC;OACL;MACDwJ,EAAE,WAAAA,GAAClF,KAAK,EAAEhK,QAAQ,EAAE;QAChB,IAAMD,SAAS,GAAG4O,YAAY,CAAC3E,KAAK,CAAC;QACrC,IAAIjK,SAAS,EAAE;UACXc,QAAQ,CAACiM,gBAAgB,CAAC/M,SAAS,EAAEC,QAAQ,CAAC;;OAErD;MACDiP,GAAG,WAAAA,IAACjF,KAAK,EAAEhK,QAAQ,EAAE;QACjB,IAAMD,SAAS,GAAG4O,YAAY,CAAC3E,KAAK,CAAC;QACrC,IAAIjK,SAAS,EAAE;UACXc,QAAQ,CAAC6O,mBAAmB,CAAC3P,SAAS,EAAEC,QAAQ,CAAC;;;KAG5D;IAED2B,MAAM,CAACgO,gBAAgB,CAACb,UAAU,EAAE;MAChCS,YAAY,EAAE;QACVpH,GAAG,WAAAA,MAAG;UACF,OAAOxK,OAAO,CAACkD,QAAQ,CAACgD,EAAE,CAAC+L,iBAAiB,CAAC,CAAC;;OAErD;MACDlS,OAAO,EAAE;QACLmS,UAAU,EAAE,IAAI;QAChB1H,GAAG,WAAAA,MAAG;UACF,OAAOtH,QAAQ,CAACgD,EAAE,CAAC+L,iBAAiB,CAAC;;OAE5C;MACDE,SAAS,EAAE;QACPD,UAAU,EAAE,IAAI;QAChB1H,GAAG,WAAAA,MAAG;;UAEF,OAAOxK,OAAO,CAACkD,QAAQ,CAACgD,EAAE,CAACkM,iBAAiB,CAAC,CAAC;;;KAGzD,CAAC;IAEF,OAAOjB,UAAU;EACrB;;EC7JA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA,IAAMkB,cAAc,GAAG;IACnBC,IAAI,EAAE,QAAQ;IACdC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,GAAG;IACjBC,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,GAAG;IACrBC,iBAAiB,EAAE,GAAG;IACtBC,gBAAgB,EAAE;EACtB,CAAC;;EAGD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;MACaC,kBAAkB,0BAAAC,YAAA;IAAAC,SAAA,CAAAF,kBAAA,EAAAC,YAAA;IAAA,IAAAE,MAAA,GAAAC,YAAA,CAAAJ,kBAAA;;EAI/B;EACA;EACA;EACA;EACA;IACI,SAAAA,mBAAYvI,OAAM,EAAE4I,YAAY,EAAwB;MAAA,IAAAC,KAAA;MAAA,IAAtBC,OAAO,GAAAxR,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;MAAA,IAAEyR,MAAM,GAAAzR,SAAA,CAAAL,MAAA,OAAAK,SAAA,MAAAC,SAAA;MAAAyR,eAAA,OAAAT,kBAAA;MAClDM,KAAA,GAAAH,MAAA,CAAAhU,IAAA;MAAQuU,eAAA,CAAAC,sBAAA,CAAAL,KAAA;MAAAI,eAAA,CAAAC,sBAAA,CAAAL,KAAA,sBA4IM,CAAC;;EAGvB;EACA;EACA;EACA;MAJII,eAAA,CAAAC,sBAAA,CAAAL,KAAA;QAAA,IAAAvG,IAAA,GAAA6G,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,CAKc,SAAAC,QAAMtJ,MAAM,EAAEuJ,aAAa;UAAA,IAAAC,eAAA;UAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAApN,IAAA;cAAA;gBAAA,MACjCsM,KAAA,CAAKgB,eAAe,KAAKN,aAAa;kBAAAI,QAAA,CAAApN,IAAA;kBAAA;;gBAAA,OAAAoN,QAAA,CAAAG,MAAA;cAAA;gBAAAH,QAAA,CAAApN,IAAA;gBAAA,OAGXyD,MAAM,CAAC+J,eAAe,EAAE;cAAA;gBAAAJ,QAAA,CAAAK,EAAA,GAAAL,QAAA,CAAAM,IAAA;gBAAAN,QAAA,CAAAO,EAAA,GAAIrB,KAAA,CAAKgB,eAAe;gBAAAF,QAAA,CAAAQ,EAAA,GAAAR,QAAA,CAAAK,EAAA,GAAAL,QAAA,CAAAO,EAAA;gBAAAP,QAAA,CAAAS,EAAA,GAAGb,aAAa;gBAAzFC,eAAe,GAAAG,QAAA,CAAAQ,EAAA,GAAAR,QAAA,CAAAS,EAAA;gBACrBvB,KAAA,CAAKrQ,GAAG,uBAAAnD,MAAA,CAAuBmU,eAAe,CAAE,CAAC;gBAACG,QAAA,CAAApN,IAAA;gBAAA,OAC5CyD,MAAM,CAACqK,eAAe,CAACb,eAAe,CAAC;cAAA;gBAC7CX,KAAA,CAAKgB,eAAe,GAAGN,aAAa;cAAC;cAAA;gBAAA,OAAAI,QAAA,CAAAW,IAAA;;aAAAhB,OAAA;SACxC;QAAA,iBAAAiB,EAAA,EAAAC,GAAA;UAAA,OAAAlI,IAAA,CAAApE,KAAA,OAAA5G,SAAA;;;MA1JGuR,KAAA,CAAKE,MAAM,GAAGA,MAAM;MACpBF,KAAA,CAAK4B,IAAI,CAAC7B,YAAY,EAAE5I,OAAM,EAAA0K,cAAA,CAAAA,cAAA,KAAO3C,cAAc,GAAKe,OAAO,CAAE,CAAC;MAAC,OAAAD,KAAA;;IACtE8B,YAAA,CAAApC,kBAAA;MAAAnO,GAAA;MAAArE,KAAA,EAED,SAAA6U,aAAa;QACT,IAAI,CAACC,aAAa,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;;;;EAKnD;EACA;EACA;EACA;EACA;;MALI1Q,GAAA;MAAArE,KAAA;QAAA,IAAAgV,KAAA,GAAA5B,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,CAMA,SAAA2B,SAAWpC,YAAY,EAAE5I,MAAM,EAAE8I,OAAO;UAAA,IAAAmC,MAAA;UAAA,IAAAC,aAAA,EAAAC,YAAA,EAAAC,mBAAA;UAAA,OAAAhC,mBAAA,GAAAK,IAAA,UAAA4B,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA1B,IAAA,GAAA0B,SAAA,CAAA/O,IAAA;cAAA;gBAAA+O,SAAA,CAAA/O,IAAA;gBAAA,OAC9B,IAAI,CAACgP,mBAAmB,CAAC3C,YAAY,EAAE,MAAM,CAAC;cAAA;gBAAA,MAEhDE,OAAO,CAACd,IAAI,KAAK,QAAQ;kBAAAsD,SAAA,CAAA/O,IAAA;kBAAA;;gBAAA+O,SAAA,CAAA/O,IAAA;gBAAA,OACnB,IAAI,CAACiP,YAAY,CAAC5C,YAAY,EAAE5I,MAAM,EAAE8I,OAAO,CAAC;cAAA;gBAChDoC,aAAa,GAAGtT,SAAS,CAACgR,YAAY,EAAE,QAAQ,EAAE;kBAAA,OAAMqC,MAAI,CAACO,YAAY,CAAC5C,YAAY,EAAE5I,MAAM,EAAE8I,OAAO,CAAC;kBAAC;gBACzGqC,YAAY,GAAG,IAAI,CAACM,wBAAwB,CAAC7C,YAAY,EAAE5I,MAAM,EAAE8I,OAAO,CAAC;gBACjF,IAAI,CAACjE,gBAAgB,CAAC,YAAY,EAAE,YAAM;kBACtCsG,YAAY,CAAC9S,MAAM,EAAE;kBACrB6S,aAAa,CAAC7S,MAAM,EAAE;iBACzB,CAAC;gBAACiT,SAAA,CAAA/O,IAAA;gBAAA;cAAA;gBAAA+O,SAAA,CAAA/O,IAAA;gBAAA,OAGG,IAAI,CAACmP,kBAAkB,CAAC9C,YAAY,EAAE5I,MAAM,CAAC;cAAA;gBAC7CoL,mBAAmB,GAAGxT,SAAS,CAACoI,MAAM,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE;kBAAA,OAAMiL,MAAI,CAACS,kBAAkB,CAAC9C,YAAY,EAAE5I,MAAM,CAAC;mBAAE,IAAI,EAAE,KAAK,CAAC;gBAC1J,IAAI,CAAC6E,gBAAgB,CAAC,YAAY,EAAE;kBAAA,OAAMuG,mBAAmB,CAAC/S,MAAM,EAAE;kBAAC;cAAC;cAAA;gBAAA,OAAAiT,SAAA,CAAAhB,IAAA;;aAAAU,QAAA;SAE/E;QAAA,SAAAP,KAAAkB,GAAA,EAAAC,GAAA,EAAAC,GAAA;UAAA,OAAAd,KAAA,CAAA7M,KAAA,OAAA5G,SAAA;;QAAA,OAAAmT,IAAA;;;EAGL;EACA;EACA;EACA;EACA;EACA;;MANIrQ,GAAA;MAAArE,KAAA;QAAA,IAAA+V,mBAAA,GAAA3C,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,CAOA,SAAA0C,SAAyBnD,YAAY,EAAE5I,MAAM;UAAA,IAAAgM,kBAAA,EAAAC,mBAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,YAAA;UAAA,OAAAhD,mBAAA,GAAAK,IAAA,UAAA4C,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA1C,IAAA,GAAA0C,SAAA,CAAA/P,IAAA;cAAA;gBAAA+P,SAAA,CAAA/P,IAAA;gBAAA,OAK/BsC,OAAO,CAAC0N,GAAG,CAAC,CAACvM,MAAM,CAACwM,cAAc,EAAE,EAAExM,MAAM,CAACyM,SAAS,EAAE,EAAEzM,MAAM,CAAC+J,eAAe,EAAE,CAAC,CAAC;cAAA;gBAAAiC,kBAAA,GAAAM,SAAA,CAAArC,IAAA;gBAAAgC,mBAAA,GAAAS,cAAA,CAAAV,kBAAA;gBAH1FE,QAAQ,GAAAD,mBAAA;gBACRE,QAAQ,GAAAF,mBAAA;gBACRG,YAAY,GAAAH,mBAAA;gBAGhBrD,YAAY,CAAC+D,MAAM,CAAC;kBAChBT,QAAQ,EAARA,QAAQ;kBACRU,QAAQ,EAAET,QAAQ,GAAG,CAAC,GAAGC;iBAC5B,CAAC;cAAC;cAAA;gBAAA,OAAAE,SAAA,CAAAhC,IAAA;;aAAAyB,QAAA;SACN;QAAA,SAAAL,mBAAAmB,GAAA,EAAAC,GAAA;UAAA,OAAAhB,mBAAA,CAAA5N,KAAA,OAAA5G,SAAA;;QAAA,OAAAoU,kBAAA;;;EAIL;EACA;EACA;EACA;EACA;EACA;EACA;;MAPItR,GAAA;MAAArE,KAAA;QAAA,IAAAgX,aAAA,GAAA5D,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,CAQA,SAAA2D,SAAmBpE,YAAY,EAAE5I,MAAM,EAAE8I,OAAO;UAAA,IAAAmE,mBAAA,EAAAf,QAAA,EAAAU,QAAA;UAAA,OAAAxD,mBAAA,GAAAK,IAAA,UAAAyD,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAvD,IAAA,GAAAuD,SAAA,CAAA5Q,IAAA;cAAA;gBAAA0Q,mBAAA,GACbrE,YAAY,CAACwE,KAAK,EAAE,EAA3ClB,QAAQ,GAAAe,mBAAA,CAARf,QAAQ,EAAEU,QAAQ,GAAAK,mBAAA,CAARL,QAAQ;gBAC1B,IAAI,OAAOV,QAAQ,KAAK,QAAQ,EAAE;kBAC9BlM,MAAM,CAAC8F,cAAc,CAACoG,QAAQ,CAAC;;gBAClC,MACG,OAAOU,QAAQ,KAAK,QAAQ;kBAAAO,SAAA,CAAA5Q,IAAA;kBAAA;;gBAAA,MACxBqQ,QAAQ,KAAK,CAAC;kBAAAO,SAAA,CAAA5Q,IAAA;kBAAA;;gBAAA4Q,SAAA,CAAA5Q,IAAA;gBAAA,OACHyD,MAAM,CAACyM,SAAS,EAAE;cAAA;gBAAAU,SAAA,CAAAnD,EAAA,GAAAmD,SAAA,CAAAlD,IAAA;gBAAA,MAAAkD,SAAA,CAAAnD,EAAA,KAAM,KAAK;kBAAAmD,SAAA,CAAA5Q,IAAA;kBAAA;;gBACpCyD,MAAM,CAACqN,KAAK,EAAE;cAAC;gBAAAF,SAAA,CAAA5Q,IAAA;gBAAA;cAAA;gBAAA,MAGdqQ,QAAQ,GAAG,CAAC;kBAAAO,SAAA,CAAA5Q,IAAA;kBAAA;;gBAAA4Q,SAAA,CAAA5Q,IAAA;gBAAA,OACNyD,MAAM,CAACyM,SAAS,EAAE;cAAA;gBAAAU,SAAA,CAAAjD,EAAA,GAAAiD,SAAA,CAAAlD,IAAA;gBAAA,MAAAkD,SAAA,CAAAjD,EAAA,KAAM,IAAI;kBAAAiD,SAAA,CAAA5Q,IAAA;kBAAA;;gBAAA4Q,SAAA,CAAA5Q,IAAA;gBAAA,OAC7ByD,MAAM,CAACsN,IAAI,EAAE,CACdpJ,KAAK;kBAAA,IAAAqJ,KAAA,GAAApE,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,CAAC,SAAAmE,SAAMvR,GAAG;oBAAA,OAAAmN,mBAAA,GAAAK,IAAA,UAAAgE,UAAAC,SAAA;sBAAA,kBAAAA,SAAA,CAAA9D,IAAA,GAAA8D,SAAA,CAAAnR,IAAA;wBAAA;0BAAA,MACRN,GAAG,CAACjC,IAAI,KAAK,iBAAiB,IAAI8O,OAAO,CAACb,aAAa;4BAAAyF,SAAA,CAAAnR,IAAA;4BAAA;;0BAAAmR,SAAA,CAAAnR,IAAA;0BAAA,OACjDyD,MAAM,CAAC2N,QAAQ,CAAC,IAAI,CAAC;wBAAA;0BAAAD,SAAA,CAAAnR,IAAA;0BAAA,OACrByD,MAAM,CAACsN,IAAI,EAAE,CAACpJ,KAAK,CAAC,UAAC0J,IAAI;4BAAA,OAAK5N,MAAM,CAAC6N,YAAY,IAAItV,OAAO,CAAC2I,KAAK,CAAC,0DAA0D,EAAE0M,IAAI,CAAC;4BAAC;wBAAA;wBAAA;0BAAA,OAAAF,SAAA,CAAApD,IAAA;;uBAAAkD,QAAA;mBAElJ;kBAAA,iBAAAM,IAAA;oBAAA,OAAAP,KAAA,CAAArP,KAAA,OAAA5G,SAAA;;oBAAC;cAAA;gBACN,IAAI,CAACkU,YAAY,CAAC5C,YAAY,EAAE5I,MAAM,EAAE8I,OAAO,CAAC;cAAC;gBAAAqE,SAAA,CAAA5Q,IAAA;gBAAA,OAE1CyD,MAAM,CAAC+J,eAAe,EAAE;cAAA;gBAAAoD,SAAA,CAAAhD,EAAA,GAAAgD,SAAA,CAAAlD,IAAA;gBAAAkD,SAAA,CAAA/C,EAAA,GAAMwC,QAAQ;gBAAA,MAAAO,SAAA,CAAAhD,EAAA,KAAAgD,SAAA,CAAA/C,EAAA;kBAAA+C,SAAA,CAAA5Q,IAAA;kBAAA;;gBAC7CyD,MAAM,CAACqK,eAAe,CAACuC,QAAQ,CAAC;cAAC;cAAA;gBAAA,OAAAO,SAAA,CAAA7C,IAAA;;aAAA0C,QAAA;SAIhD;QAAA,SAAAxB,aAAAuC,GAAA,EAAAC,GAAA,EAAAC,IAAA;UAAA,OAAAlB,aAAA,CAAA7O,KAAA,OAAA5G,SAAA;;QAAA,OAAAkU,YAAA;;;EAGL;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MAXIpR,GAAA;MAAArE,KAAA,EAYA,SAAA0V,yBAAyB7C,YAAY,EAAE5I,MAAM,EAAE8I,OAAO,EAAE;QAAA,IAAAoF,MAAA;QACpD,IAAQhG,YAAY,GAA6EY,OAAO,CAAhGZ,YAAY;UAAEC,eAAe,GAA4DW,OAAO,CAAlFX,eAAe;UAAEC,gBAAgB,GAA0CU,OAAO,CAAjEV,gBAAgB;UAAEC,iBAAiB,GAAuBS,OAAO,CAA/CT,iBAAiB;UAAEC,gBAAgB,GAAKQ,OAAO,CAA5BR,gBAAgB;QAC5F,IAAM6F,YAAY,GAAGhY,IAAI,CAACiY,GAAG,CAAC9F,gBAAgB,EAAEnS,IAAI,CAACkY,GAAG,CAACjG,gBAAgB,EAAED,eAAe,CAAC,CAAC,GAAG,IAAI;QAEnG,IAAMmG,KAAK;UAAA,IAAAC,KAAA,GAAApF,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,CAAG,SAAAmF;YAAA,IAAAC,IAAA,EAAAC,OAAA,EAAAN,GAAA,EAAAC,GAAA,EAAAM,UAAA;YAAA,OAAAvF,mBAAA,GAAAK,IAAA,UAAAmF,UAAAC,SAAA;cAAA,kBAAAA,SAAA,CAAAjF,IAAA,GAAAiF,SAAA,CAAAtS,IAAA;gBAAA;kBAAAsS,SAAA,CAAA7E,EAAA,GACNpB,YAAY,CAACwE,KAAK,EAAE,CAACR,QAAQ,KAAK,CAAC;kBAAA,IAAAiC,SAAA,CAAA7E,EAAA;oBAAA6E,SAAA,CAAAtS,IAAA;oBAAA;;kBAAAsS,SAAA,CAAAtS,IAAA;kBAAA,OAAUyD,MAAM,CAACyM,SAAS,EAAE;gBAAA;kBAAAoC,SAAA,CAAA3E,EAAA,GAAA2E,SAAA,CAAA5E,IAAA;kBAAA4E,SAAA,CAAA7E,EAAA,GAAA6E,SAAA,CAAA3E,EAAA,KAAK,IAAI;gBAAA;kBAAA,KAAA2E,SAAA,CAAA7E,EAAA;oBAAA6E,SAAA,CAAAtS,IAAA;oBAAA;;kBAAA,OAAAsS,SAAA,CAAA/E,MAAA;gBAAA;kBAAA+E,SAAA,CAAA1E,EAAA,GAG/DvB,YAAY,CAACwE,KAAK,EAAE,CAAClB,QAAQ;kBAAA2C,SAAA,CAAAtS,IAAA;kBAAA,OAAUyD,MAAM,CAACwM,cAAc,EAAE;gBAAA;kBAAAqC,SAAA,CAAAzE,EAAA,GAAAyE,SAAA,CAAA5E,IAAA;kBAArEwE,IAAI,GAAAI,SAAA,CAAA1E,EAAA,GAAA0E,SAAA,CAAAzE,EAAA;kBACJsE,OAAO,GAAGvY,IAAI,CAAC2Y,GAAG,CAACL,IAAI,CAAC;kBAC9BP,MAAI,CAAC1V,GAAG,WAAAnD,MAAA,CAAWoZ,IAAI,CAAE,CAAC;kBAAC,MACvBC,OAAO,GAAGvG,eAAe;oBAAA0G,SAAA,CAAAtS,IAAA;oBAAA;;kBAAAsS,SAAA,CAAAtS,IAAA;kBAAA,OACnB2R,MAAI,CAACa,WAAW,CAAC/O,MAAM,EAAE,CAAC,CAAC;gBAAA;kBACjCA,MAAM,CAAC8F,cAAc,CAAC8C,YAAY,CAACwE,KAAK,EAAE,CAAClB,QAAQ,CAAC;kBACpDgC,MAAI,CAAC1V,GAAG,CAAC,uBAAuB,CAAC;kBAACqW,SAAA,CAAAtS,IAAA;kBAAA;gBAAA;kBAAA,MAE7BmS,OAAO,GAAGxG,YAAY;oBAAA2G,SAAA,CAAAtS,IAAA;oBAAA;;kBACrB6R,GAAG,GAAGM,OAAO,GAAGpG,gBAAgB;kBAChC+F,GAAG,GAAGhG,iBAAiB;kBACvBsG,UAAU,GAAGP,GAAG,GAAGC,GAAG,GAAG,CAACA,GAAG,GAAGD,GAAG,IAAI,CAAC,GAAGC,GAAG;kBAAAQ,SAAA,CAAAtS,IAAA;kBAAA,OAC9C2R,MAAI,CAACa,WAAW,CAAC/O,MAAM,EAAE2O,UAAU,GAAGxY,IAAI,CAAC6Y,IAAI,CAACP,IAAI,CAAC,CAAC;gBAAA;kBAC5DP,MAAI,CAAC1V,GAAG,CAAC,wBAAwB,CAAC;gBAAC;gBAAA;kBAAA,OAAAqW,SAAA,CAAAvE,IAAA;;eAAAkE,QAAA;WAE1C;UAAA,gBAnBKF,KAAKA;YAAA,OAAAC,KAAA,CAAArQ,KAAA,OAAA5G,SAAA;;WAmBV;QACD,IAAM2X,QAAQ,GAAGC,WAAW,CAAC;UAAA,OAAMZ,KAAK,EAAE;WAAEH,YAAY,CAAC;QACzD,OAAO;UAAE9V,MAAM,EAAE,SAAAA;YAAA,OAAM8W,aAAa,CAACF,QAAQ,CAAC;;SAAE;;;;EAIxD;EACA;;MAFI7U,GAAA;MAAArE,KAAA,EAGA,SAAAyC,IAAIkF,GAAG,EAAE;QAAA,IAAA0R,YAAA;QACL,CAAAA,YAAA,OAAI,CAACrG,MAAM,cAAAqG,YAAA,uBAAXA,YAAA,CAAA1a,IAAA,KAAI,yBAAAW,MAAA,CAAiCqI,GAAG,CAAE,CAAC;;;MAC9CtD,GAAA;MAAArE,KAAA;;EAoBL;EACA;EACA;EACA;MACI,SAAAwV,oBAAoB3C,YAAY,EAAExL,KAAK,EAAE;QACrC,OAAO,IAAIyB,OAAO,CAAC,UAACjB,OAAO,EAAK;UAC5B,IAAM0Q,KAAK,GAAG,SAARA,KAAKA,GAAS;YAChB,IAAI1F,YAAY,CAACyG,UAAU,KAAKjS,KAAK,EAAE;cACnCQ,OAAO,EAAE;aACZ,MACI;cACDgL,YAAY,CAAC/D,gBAAgB,CAAC,kBAAkB,EAAEyJ,KAAK,EAAE;gBAAEgB,IAAI,EAAE;eAAM,CAAC;;WAE/E;UACDhB,KAAK,EAAE;SACV,CAAC;;;IACL,OAAA/F,kBAAA;EAAA,gBAAAgH,gBAAA,CAxLmCC,WAAW;;ECpBnD,IAAMC,SAAS,GAAG,IAAIjW,OAAO,EAAE;EAC/B,IAAMkW,QAAQ,GAAG,IAAIlW,OAAO,EAAE;EAC9B,IAAIqN,UAAU,GAAG,EAAE;MAEb5B,MAAM;;EAEZ;EACA;EACA;EACA;EACA;EACA;EACA;IACI,SAAAA,OAAYxP,OAAO,EAAgB;MAAA,IAAAoT,KAAA;MAAA,IAAdC,OAAO,GAAAxR,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;MAAA0R,eAAA,OAAA/D,MAAA;MAC7B,IAAI,CAAC4I,YAAY,GAAG/E,OAAO,CAAC6G,OAAO,IAAI7G,OAAO,CAAC6G,OAAO,KAAKpY,SAAS;;;MAGpE,IAAI8B,MAAM,CAACuW,MAAM,IAAIna,OAAO,YAAYma,MAAM,EAAE;QAC5C,IAAIna,OAAO,CAACwB,MAAM,GAAG,CAAC,IAAIoC,MAAM,CAACd,OAAO,IAAIA,OAAO,CAAC4I,IAAI,IAAI,IAAI,CAAC0M,YAAY,EAAE;UAC3EtV,OAAO,CAAC4I,IAAI,CAAC,6EAA6E,CAAC;;QAG/F1L,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;;;;MAIxB,IAAI,OAAOmD,QAAQ,KAAK,WAAW,IAAI,OAAOnD,OAAO,KAAK,QAAQ,EAAE;QAChEA,OAAO,GAAGmD,QAAQ,CAACiX,cAAc,CAACpa,OAAO,CAAC;;;;MAI9C,IAAI,CAACD,YAAY,CAACC,OAAO,CAAC,EAAE;QACxB,MAAM,IAAIkC,SAAS,CAAC,qDAAqD,CAAC;;;;MAI9E,IAAIlC,OAAO,CAACqa,QAAQ,KAAK,QAAQ,EAAE;QAC/B,IAAMC,MAAM,GAAGta,OAAO,CAAC+M,aAAa,CAAC,QAAQ,CAAC;QAE9C,IAAIuN,MAAM,EAAE;UACRta,OAAO,GAAGsa,MAAM;;;;;MAKxB,IAAIta,OAAO,CAACqa,QAAQ,KAAK,QAAQ,IAAI,CAACzZ,UAAU,CAACZ,OAAO,CAAC2M,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE;QACjF,MAAM,IAAI1K,KAAK,CAAC,gDAAgD,CAAC;;;;MAIrE,IAAI+X,SAAS,CAACO,GAAG,CAACva,OAAO,CAAC,EAAE;QACxB,OAAOga,SAAS,CAACvP,GAAG,CAACzK,OAAO,CAAC;;MAGjC,IAAI,CAACwa,OAAO,GAAGxa,OAAO,CAACG,aAAa,CAACC,WAAW;MAChD,IAAI,CAACJ,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACkM,MAAM,GAAG,GAAG;MAEjB,IAAMuO,YAAY,GAAG,IAAIrR,OAAO,CAAC,UAACjB,OAAO,EAAEH,MAAM,EAAK;QAClDoL,KAAI,CAACsH,UAAU,GAAG,UAACpO,KAAK,EAAK;UACzB,IAAI,CAAC1L,UAAU,CAAC0L,KAAK,CAACJ,MAAM,CAAC,IAAIkH,KAAI,CAACpT,OAAO,CAACuD,aAAa,KAAK+I,KAAK,CAACwC,MAAM,EAAE;YAC1E;;UAGJ,IAAIsE,KAAI,CAAClH,MAAM,KAAK,GAAG,EAAE;YACrBkH,KAAI,CAAClH,MAAM,GAAGI,KAAK,CAACJ,MAAM;;UAG9B,IAAMZ,IAAI,GAAGD,gBAAgB,CAACiB,KAAK,CAAChB,IAAI,CAAC;UACzC,IAAMqP,OAAO,GAAGrP,IAAI,IAAIA,IAAI,CAACgB,KAAK,KAAK,OAAO;UAC9C,IAAMsO,YAAY,GAAGD,OAAO,IAAIrP,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACA,IAAI,CAACK,MAAM,KAAK,OAAO;UAEzE,IAAIiP,YAAY,EAAE;YACd,IAAMnP,KAAK,GAAG,IAAIxJ,KAAK,CAACqJ,IAAI,CAACA,IAAI,CAACO,OAAO,CAAC;YAC1CJ,KAAK,CAAClH,IAAI,GAAG+G,IAAI,CAACA,IAAI,CAAC/G,IAAI;YAC3ByD,MAAM,CAACyD,KAAK,CAAC;YACb;;UAGJ,IAAMoP,YAAY,GAAGvP,IAAI,IAAIA,IAAI,CAACgB,KAAK,KAAK,OAAO;UACnD,IAAMwO,cAAc,GAAGxP,IAAI,IAAIA,IAAI,CAACK,MAAM,KAAK,MAAM;UAErD,IAAIkP,YAAY,IAAIC,cAAc,EAAE;YAChC1H,KAAI,CAACpT,OAAO,CAACqN,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;YAC/ClF,OAAO,EAAE;YACT;;UAGJgE,WAAW,CAACiH,KAAI,EAAE9H,IAAI,CAAC;SAC1B;QAED8H,KAAI,CAACoH,OAAO,CAACpL,gBAAgB,CAAC,SAAS,EAAEgE,KAAI,CAACsH,UAAU,CAAC;QAEzD,IAAItH,KAAI,CAACpT,OAAO,CAACqa,QAAQ,KAAK,QAAQ,EAAE;UACpC,IAAMzO,MAAM,GAAGY,mBAAmB,CAACxM,OAAO,EAAEqT,OAAO,CAAC;UACpD,IAAMxS,GAAG,GAAGc,WAAW,CAACiK,MAAM,CAAC;UAE/B0B,aAAa,CAACzM,GAAG,EAAE+K,MAAM,EAAE5L,OAAO,CAAC,CAACuH,IAAI,CAAC,UAAC+D,IAAI,EAAK;YAC/C,IAAMgP,MAAM,GAAG1N,WAAW,CAACtB,IAAI,EAAEtL,OAAO,CAAC;;;YAGzCoT,KAAI,CAACpT,OAAO,GAAGsa,MAAM;YACrBlH,KAAI,CAAC2H,gBAAgB,GAAG/a,OAAO;YAE/BiL,aAAa,CAACjL,OAAO,EAAEsa,MAAM,CAAC;YAC9BN,SAAS,CAACtP,GAAG,CAAC0I,KAAI,CAACpT,OAAO,EAAEoT,KAAI,CAAC;YAEjC,OAAO9H,IAAI;WACd,CAAC,CAACmD,KAAK,CAACzG,MAAM,CAAC;;OAEvB,CAAC;;;MAGFiS,QAAQ,CAACvP,GAAG,CAAC,IAAI,EAAE+P,YAAY,CAAC;MAChCT,SAAS,CAACtP,GAAG,CAAC,IAAI,CAAC1K,OAAO,EAAE,IAAI,CAAC;;;;MAIjC,IAAI,IAAI,CAACA,OAAO,CAACqa,QAAQ,KAAK,QAAQ,EAAE;QACpCxW,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC;;MAG7B,IAAIuN,UAAU,CAACgB,SAAS,EAAE;QACtB,IAAML,cAAc,GAAG,SAAjBA,cAAcA;UAAA,OAASX,UAAU,CAACQ,IAAI,EAAE;;QAC9C,IAAI,CAACoJ,uBAAuB,GAAG,YAAM;UACjC,IAAI5J,UAAU,CAACS,YAAY,EAAE;YACzBvH,aAAa,CAAC8I,KAAI,EAAE,sBAAsB,EAAErB,cAAc,CAAC;WAC9D,MACI;YACDnH,cAAc,CAACwI,KAAI,EAAE,sBAAsB,EAAErB,cAAc,CAAC;;;UAGhEqB,KAAI,CAAC6H,KAAK,EAAE,CAAC1T,IAAI,CAAC,YAAM;YACpB1D,WAAW,CAACuP,KAAI,EAAE,kBAAkB,EAAEhC,UAAU,CAACS,YAAY,CAAC;WACjE,CAAC;SACL;QAEDT,UAAU,CAACI,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAACwJ,uBAAuB,CAAC;;MAGnE,IAAI,IAAI,CAAC5C,YAAY,EAAE;QACnBvV,aAAa,EAAE;;MAGnB,OAAO,IAAI;;;;EAInB;EACA;EACA;EACA;EACA;IALIqS,YAAA,CAAA1F,MAAA;MAAA7K,GAAA;MAAArE,KAAA;;EAWJ;EACA;EACA;EACA;EACA;EACA;MACI,SAAAmP,WAAWlL,IAAI,EAAW;QAAA,IAAAiR,MAAA;QAAA,SAAA0F,IAAA,GAAArZ,SAAA,CAAAL,MAAA,EAAN2Z,IAAI,OAAA1X,KAAA,CAAAyX,IAAA,OAAAA,IAAA,WAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;UAAJD,IAAI,CAAAC,IAAA,QAAAvZ,SAAA,CAAAuZ,IAAA;;QACpB,IAAI7W,IAAI,KAAKzC,SAAS,IAAIyC,IAAI,KAAK,IAAI,EAAE;UACrC,MAAM,IAAIrC,SAAS,CAAC,8BAA8B,CAAC;;QAGvD,OAAO,IAAIkH,OAAO,CAAC,UAACjB,OAAO,EAAEH,MAAM,EAAK;;;UAGpC,OAAOwN,MAAI,CAACyF,KAAK,EAAE,CAAC1T,IAAI,CAAC,YAAM;YAC3B+C,aAAa,CAACkL,MAAI,EAAEjR,IAAI,EAAE;cACtB4D,OAAO,EAAPA,OAAO;cACPH,MAAM,EAANA;aACH,CAAC;YAEF,IAAImT,IAAI,CAAC3Z,MAAM,KAAK,CAAC,EAAE;cACnB2Z,IAAI,GAAG,EAAE;aACZ,MACI,IAAIA,IAAI,CAAC3Z,MAAM,KAAK,CAAC,EAAE;cACxB2Z,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC;;YAGlBtX,WAAW,CAAC2R,MAAI,EAAEjR,IAAI,EAAE4W,IAAI,CAAC;WAChC,CAAC,CAAC1M,KAAK,CAACzG,MAAM,CAAC;SACnB,CAAC;;;EAGV;EACA;EACA;EACA;EACA;;MALIrD,GAAA;MAAArE,KAAA,EAMA,SAAAmK,IAAIlG,IAAI,EAAE;QAAA,IAAAkU,MAAA;QACN,OAAO,IAAIrP,OAAO,CAAC,UAACjB,OAAO,EAAEH,MAAM,EAAK;UACpCzD,IAAI,GAAGhF,aAAa,CAACgF,IAAI,EAAE,KAAK,CAAC;;;;UAIjC,OAAOkU,MAAI,CAACwC,KAAK,EAAE,CAAC1T,IAAI,CAAC,YAAM;YAC3B+C,aAAa,CAACmO,MAAI,EAAElU,IAAI,EAAE;cACtB4D,OAAO,EAAPA,OAAO;cACPH,MAAM,EAANA;aACH,CAAC;YAEFnE,WAAW,CAAC4U,MAAI,EAAElU,IAAI,CAAC;WAC1B,CAAC,CAACkK,KAAK,CAACzG,MAAM,CAAC;SACnB,CAAC;;;;EAIV;EACA;EACA;EACA;EACA;EACA;;MANIrD,GAAA;MAAArE,KAAA,EAOA,SAAAoK,IAAInG,IAAI,EAAEjE,KAAK,EAAE;QAAA,IAAA+a,MAAA;QACb,OAAO,IAAIjS,OAAO,CAAC,UAACjB,OAAO,EAAEH,MAAM,EAAK;UACpCzD,IAAI,GAAGhF,aAAa,CAACgF,IAAI,EAAE,KAAK,CAAC;UAEjC,IAAIjE,KAAK,KAAKwB,SAAS,IAAIxB,KAAK,KAAK,IAAI,EAAE;YACvC,MAAM,IAAI4B,SAAS,CAAC,+BAA+B,CAAC;;;;;UAKxD,OAAOmZ,MAAI,CAACJ,KAAK,EAAE,CAAC1T,IAAI,CAAC,YAAM;YAC3B+C,aAAa,CAAC+Q,MAAI,EAAE9W,IAAI,EAAE;cACtB4D,OAAO,EAAPA,OAAO;cACPH,MAAM,EAANA;aACH,CAAC;YAEFnE,WAAW,CAACwX,MAAI,EAAE9W,IAAI,EAAEjE,KAAK,CAAC;WACjC,CAAC,CAACmO,KAAK,CAACzG,MAAM,CAAC;SACnB,CAAC;;;;EAIV;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MARIrD,GAAA;MAAArE,KAAA,EASA,SAAAkR,GAAGnP,SAAS,EAAEC,QAAQ,EAAE;QACpB,IAAI,CAACD,SAAS,EAAE;UACZ,MAAM,IAAIH,SAAS,CAAC,8BAA8B,CAAC;;QAGvD,IAAI,CAACI,QAAQ,EAAE;UACX,MAAM,IAAIJ,SAAS,CAAC,oCAAoC,CAAC;;QAG7D,IAAI,OAAOI,QAAQ,KAAK,UAAU,EAAE;UAChC,MAAM,IAAIJ,SAAS,CAAC,kCAAkC,CAAC;;QAG3D,IAAMkK,SAAS,GAAGzB,YAAY,CAAC,IAAI,WAAA/K,MAAA,CAAWyC,SAAS,CAAE,CAAC;QAC1D,IAAI+J,SAAS,CAAC5K,MAAM,KAAK,CAAC,EAAE;UACxB,IAAI,CAACiO,UAAU,CAAC,kBAAkB,EAAEpN,SAAS,CAAC,CAACoM,KAAK,CAAC,YAAM;;;WAG1D,CAAC;;QAGNnE,aAAa,CAAC,IAAI,WAAA1K,MAAA,CAAWyC,SAAS,GAAIC,QAAQ,CAAC;;;;EAI3D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MARIqC,GAAA;MAAArE,KAAA,EASA,SAAAiR,IAAIlP,SAAS,EAAEC,QAAQ,EAAE;QACrB,IAAI,CAACD,SAAS,EAAE;UACZ,MAAM,IAAIH,SAAS,CAAC,8BAA8B,CAAC;;QAGvD,IAAII,QAAQ,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UAC5C,MAAM,IAAIJ,SAAS,CAAC,kCAAkC,CAAC;;QAG3D,IAAMoZ,YAAY,GAAG1Q,cAAc,CAAC,IAAI,WAAAhL,MAAA,CAAWyC,SAAS,GAAIC,QAAQ,CAAC;;;QAGzE,IAAIgZ,YAAY,EAAE;UACd,IAAI,CAAC7L,UAAU,CAAC,qBAAqB,EAAEpN,SAAS,CAAC,CAACoM,KAAK,CAAC,UAACpK,CAAC,EAAK;;;WAG9D,CAAC;;;;;EAKd;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;MAPIM,GAAA;MAAArE,KAAA,EAQA,SAAAib,UAAUlI,OAAO,EAAE;QACf,OAAO,IAAI,CAAC5D,UAAU,CAAC,WAAW,EAAE4D,OAAO,CAAC;;;;EAIpD;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;MANI1O,GAAA;MAAArE,KAAA,EAOA,SAAA2a,QAAQ;QACJ,IAAMR,YAAY,GAAGR,QAAQ,CAACxP,GAAG,CAAC,IAAI,CAAC,IAAI,IAAIrB,OAAO,CAAC,UAACjB,OAAO,EAAEH,MAAM,EAAK;UACxEA,MAAM,CAAC,IAAI/F,KAAK,CAAC,oCAAoC,CAAC,CAAC;SAC1D,CAAC;QACF,OAAOmH,OAAO,CAACjB,OAAO,CAACsS,YAAY,CAAC;;;;EAI5C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;MANI9V,GAAA;MAAArE,KAAA,EAOA,SAAAkb,YAAYC,IAAI,EAAa;QAAA,IAAXnQ,IAAI,GAAAzJ,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;QACvB,OAAO,IAAI,CAAC4N,UAAU,CAAC,aAAa,EAAE;UAAEgM,IAAI,EAAJA,IAAI;UAAEnQ,IAAI,EAAJA;SAAM,CAAC;;;;EAI7D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;MALI3G,GAAA;MAAArE,KAAA,EAMA,SAAAob,eAAe3Z,EAAE,EAAE;QACf,OAAO,IAAI,CAAC0N,UAAU,CAAC,gBAAgB,EAAE1N,EAAE,CAAC;;;;EAIpD;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MAVI4C,GAAA;MAAArE,KAAA,EAWA,SAAAqb,gBAAgBC,QAAQ,EAAEC,IAAI,EAAE;QAC5B,IAAI,CAACD,QAAQ,EAAE;UACX,MAAM,IAAI1Z,SAAS,CAAC,2BAA2B,CAAC;;QAGpD,OAAO,IAAI,CAACuN,UAAU,CAAC,iBAAiB,EAAE;UACtCmM,QAAQ,EAARA,QAAQ;UACRC,IAAI,EAAJA;SACH,CAAC;;;;EAIV;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJIlX,GAAA;MAAArE,KAAA,EAKA,SAAAwb,mBAAmB;QACf,OAAO,IAAI,CAACrM,UAAU,CAAC,kBAAkB,CAAC;;;;EAIlD;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9K,GAAA;MAAArE,KAAA,EAKA,SAAAsX,QAAQ;QACJ,OAAO,IAAI,CAACnI,UAAU,CAAC,OAAO,CAAC;;;;EAIvC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;MAPI9K,GAAA;MAAArE,KAAA,EAQA,SAAAuX,OAAO;QACH,OAAO,IAAI,CAACpI,UAAU,CAAC,MAAM,CAAC;;;;EAItC;EACA;EACA;;MAHI9K,GAAA;MAAArE,KAAA,EAIA,SAAAqR,oBAAoB;QAChB,IAAIP,UAAU,CAACgB,SAAS,EAAE;UACtB,OAAOhB,UAAU,CAACC,OAAO,CAAC,IAAI,CAACrR,OAAO,CAAC;;QAE3C,OAAO,IAAI,CAACyP,UAAU,CAAC,mBAAmB,CAAC;;;;EAInD;EACA;EACA;;MAHI9K,GAAA;MAAArE,KAAA,EAIA,SAAAyR,iBAAiB;QACb,IAAIX,UAAU,CAACgB,SAAS,EAAE;UACtB,OAAOhB,UAAU,CAACQ,IAAI,EAAE;;QAE5B,OAAO,IAAI,CAACnC,UAAU,CAAC,gBAAgB,CAAC;;;;EAIhD;EACA;EACA;;MAHI9K,GAAA;MAAArE,KAAA,EAIA,SAAAyb,gBAAgB;QACZ,IAAI3K,UAAU,CAACgB,SAAS,EAAE;UACtB,OAAOhJ,OAAO,CAACjB,OAAO,CAACiJ,UAAU,CAACS,YAAY,CAAC;;QAEnD,OAAO,IAAI,CAACpH,GAAG,CAAC,YAAY,CAAC;;;;EAIrC;EACA;EACA;;MAHI9F,GAAA;MAAArE,KAAA,EAIA,SAAA0b,0BAA0B;QACtB,OAAO,IAAI,CAACvM,UAAU,CAAC,yBAAyB,CAAC;;;;EAIzD;EACA;EACA;;MAHI9K,GAAA;MAAArE,KAAA,EAIA,SAAA2b,uBAAuB;QACnB,OAAO,IAAI,CAACxM,UAAU,CAAC,sBAAsB,CAAC;;;;EAItD;EACA;EACA;;MAHI9K,GAAA;MAAArE,KAAA,EAIA,SAAA4b,sBAAsB;QAClB,OAAO,IAAI,CAACzR,GAAG,CAAC,kBAAkB,CAAC;;;;EAI3C;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAA6b,uBAAuB;QACnB,OAAO,IAAI,CAAC1M,UAAU,CAAC,sBAAsB,CAAC;;;;EAItD;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9K,GAAA;MAAArE,KAAA,EAKA,SAAA8b,SAAS;QACL,OAAO,IAAI,CAAC3M,UAAU,CAAC,QAAQ,CAAC;;;;EAIxC;EACA;EACA;EACA;EACA;EACA;EACA;;MAPI9K,GAAA;MAAArE,KAAA,EAQA,SAAA+b,UAAU;QAAA,IAAAC,MAAA;QACN,OAAO,IAAIlT,OAAO,CAAC,UAACjB,OAAO,EAAK;UAC5B8R,QAAQ,CAAC7O,MAAM,CAACkR,MAAI,CAAC;UACrBtC,SAAS,CAAC5O,MAAM,CAACkR,MAAI,CAACtc,OAAO,CAAC;UAE9B,IAAIsc,MAAI,CAACvB,gBAAgB,EAAE;YACvBf,SAAS,CAAC5O,MAAM,CAACkR,MAAI,CAACvB,gBAAgB,CAAC;YACvCuB,MAAI,CAACvB,gBAAgB,CAACwB,eAAe,CAAC,wBAAwB,CAAC;;UAGnE,IAAID,MAAI,CAACtc,OAAO,IAAIsc,MAAI,CAACtc,OAAO,CAACqa,QAAQ,KAAK,QAAQ,IAAIiC,MAAI,CAACtc,OAAO,CAACwc,UAAU,EAAE;;;YAG/E,IAAIF,MAAI,CAACtc,OAAO,CAACwc,UAAU,CAACA,UAAU,IAAIF,MAAI,CAACvB,gBAAgB,IAAIuB,MAAI,CAACvB,gBAAgB,KAAKuB,MAAI,CAACtc,OAAO,CAACwc,UAAU,EAAE;cAClHF,MAAI,CAACtc,OAAO,CAACwc,UAAU,CAACA,UAAU,CAACC,WAAW,CAACH,MAAI,CAACtc,OAAO,CAACwc,UAAU,CAAC;aAC1E,MACI;cACDF,MAAI,CAACtc,OAAO,CAACwc,UAAU,CAACC,WAAW,CAACH,MAAI,CAACtc,OAAO,CAAC;;;;;;UAMzD,IAAIsc,MAAI,CAACtc,OAAO,IAAIsc,MAAI,CAACtc,OAAO,CAACqa,QAAQ,KAAK,KAAK,IAAIiC,MAAI,CAACtc,OAAO,CAACwc,UAAU,EAAE;YAC5EF,MAAI,CAACtc,OAAO,CAACuc,eAAe,CAAC,wBAAwB,CAAC;YACtD,IAAMjC,MAAM,GAAGgC,MAAI,CAACtc,OAAO,CAAC+M,aAAa,CAAC,QAAQ,CAAC;YACnD,IAAIuN,MAAM,IAAIA,MAAM,CAACkC,UAAU,EAAE;;;cAG7B,IAAIlC,MAAM,CAACkC,UAAU,CAACA,UAAU,IAAIF,MAAI,CAACvB,gBAAgB,IAAIuB,MAAI,CAACvB,gBAAgB,KAAKT,MAAM,CAACkC,UAAU,EAAE;gBACtGlC,MAAM,CAACkC,UAAU,CAACA,UAAU,CAACC,WAAW,CAACnC,MAAM,CAACkC,UAAU,CAAC;eAC9D,MACI;gBACDlC,MAAM,CAACkC,UAAU,CAACC,WAAW,CAACnC,MAAM,CAAC;;;;UAKjDgC,MAAI,CAAC9B,OAAO,CAACxI,mBAAmB,CAAC,SAAS,EAAEsK,MAAI,CAAC5B,UAAU,CAAC;UAE5D,IAAItJ,UAAU,CAACgB,SAAS,EAAE;YACtBhB,UAAU,CAACG,GAAG,CAAC,kBAAkB,EAAE+K,MAAI,CAACtB,uBAAuB,CAAC;;UAGpE7S,OAAO,EAAE;SACZ,CAAC;;;;EAIV;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJIxD,GAAA;MAAArE,KAAA,EAKA,SAAAoc,eAAe;QACX,OAAO,IAAI,CAACjS,GAAG,CAAC,WAAW,CAAC;;;;EAIpC;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MAVI9F,GAAA;MAAArE,KAAA,EAWA,SAAAqc,aAAaC,SAAS,EAAE;QACpB,OAAO,IAAI,CAAClS,GAAG,CAAC,WAAW,EAAEkS,SAAS,CAAC;;;;EAI/C;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJIjY,GAAA;MAAArE,KAAA,EAKA,SAAAuc,cAAc;QACV,OAAO,IAAI,CAACpS,GAAG,CAAC,UAAU,CAAC;;;;EAInC;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAAwc,iBAAiB;QACb,OAAO,IAAI,CAACrS,GAAG,CAAC,aAAa,CAAC;;;;EAItC;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;MALI9F,GAAA;MAAArE,KAAA,EAMA,SAAAyc,eAAeC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACtS,GAAG,CAAC,aAAa,EAAEsS,MAAM,CAAC;;;;EAI9C;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJIrY,GAAA;MAAArE,KAAA,EAKA,SAAA2c,cAAc;QACV,OAAO,IAAI,CAACxS,GAAG,CAAC,UAAU,CAAC;;;;EAInC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAA4c,oBAAoB;QAChB,OAAO,IAAI,CAACzS,GAAG,CAAC,gBAAgB,CAAC;;;;EAIzC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAA6c,WAAW;QACP,OAAO,IAAI,CAAC1S,GAAG,CAAC,OAAO,CAAC;;;;EAIhC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAA8c,YAAY;QACR,OAAOhU,OAAO,CAAC0N,GAAG,CAAC,CACf,IAAI,CAACrM,GAAG,CAAC,UAAU,CAAC,EACpB,IAAI,CAACA,GAAG,CAAC,UAAU,CAAC,EACpB,IAAI,CAACA,GAAG,CAAC,YAAY,CAAC,EACtB,IAAI,CAACA,GAAG,CAAC,WAAW,CAAC,CACxB,CAAC;;;;EAIV;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MARI9F,GAAA;MAAArE,KAAA,EASA,SAAA+c,SAASC,KAAK,EAAE;QACZ,OAAO,IAAI,CAAC5S,GAAG,CAAC,OAAO,EAAE4S,KAAK,CAAC;;;;EAIvC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MATI3Y,GAAA;MAAArE,KAAA,EAUA,SAAAid,UAAUC,MAAM,EAAE;QACd,IAAI,CAAC/Z,KAAK,CAACga,OAAO,CAACD,MAAM,CAAC,EAAE;UACxB,OAAO,IAAIpU,OAAO,CAAC,UAACjB,OAAO,EAAEH,MAAM;YAAA,OAAKA,MAAM,CAAC,IAAI9F,SAAS,CAAC,4BAA4B,CAAC,CAAC;YAAC;;QAGhG,IAAMwb,WAAW,GAAG,IAAItU,OAAO,CAAC,UAACjB,OAAO;UAAA,OAAKA,OAAO,CAAC,IAAI,CAAC;UAAC;QAC3D,IAAMwV,aAAa,GAAG,CAClBH,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC9S,GAAG,CAAC,UAAU,EAAE8S,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGE,WAAW,EACzDF,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC9S,GAAG,CAAC,UAAU,EAAE8S,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGE,WAAW,EACzDF,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC9S,GAAG,CAAC,YAAY,EAAE8S,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGE,WAAW,EAC3DF,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC9S,GAAG,CAAC,WAAW,EAAE8S,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGE,WAAW,CAC7D;QACD,OAAOtU,OAAO,CAAC0N,GAAG,CAAC6G,aAAa,CAAC;;;;EAIzC;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJIhZ,GAAA;MAAArE,KAAA,EAKA,SAAAsd,eAAe;QACX,OAAO,IAAI,CAACnT,GAAG,CAAC,WAAW,CAAC;;;;EAIpC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAAyW,iBAAiB;QACb,OAAO,IAAI,CAACtM,GAAG,CAAC,aAAa,CAAC;;;;EAItC;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MAXI9F,GAAA;MAAArE,KAAA,EAYA,SAAA+P,eAAewN,WAAW,EAAE;QACxB,OAAO,IAAI,CAACnT,GAAG,CAAC,aAAa,EAAEmT,WAAW,CAAC;;;;EAInD;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;MANIlZ,GAAA;MAAArE,KAAA,EAOA,SAAAwd,cAAc;QACV,OAAO,IAAI,CAACrT,GAAG,CAAC,UAAU,CAAC;;;;EAInC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;MALI9F,GAAA;MAAArE,KAAA,EAMA,SAAAyd,WAAW;QACP,OAAO,IAAI,CAACtT,GAAG,CAAC,OAAO,CAAC;;;;EAIhC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAA0d,UAAU;QACN,OAAO,IAAI,CAACvT,GAAG,CAAC,MAAM,CAAC;;;;EAI/B;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;MANI9F,GAAA;MAAArE,KAAA,EAOA,SAAA2d,QAAQC,IAAI,EAAE;QACV,OAAO,IAAI,CAACxT,GAAG,CAAC,MAAM,EAAEwT,IAAI,CAAC;;;;EAKrC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;MANIvZ,GAAA;MAAArE,KAAA,EAOA,SAAA4X,SAASiG,KAAK,EAAE;QACZ,OAAO,IAAI,CAACzT,GAAG,CAAC,OAAO,EAAEyT,KAAK,CAAC;;;;EAIvC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJIxZ,GAAA;MAAArE,KAAA,EAKA,SAAA8d,WAAW;QACP,OAAO,IAAI,CAAC3T,GAAG,CAAC,OAAO,CAAC;;;;EAIhC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAA0W,YAAY;QACR,OAAO,IAAI,CAACvM,GAAG,CAAC,QAAQ,CAAC;;;;EAIjC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAAgU,kBAAkB;QACd,OAAO,IAAI,CAAC7J,GAAG,CAAC,cAAc,CAAC;;;;EAIvC;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;MAPI9F,GAAA;MAAArE,KAAA,EAQA,SAAAsU,gBAAgB+B,YAAY,EAAE;QAC1B,OAAO,IAAI,CAACjM,GAAG,CAAC,cAAc,EAAEiM,YAAY,CAAC;;;;EAIrD;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJIhS,GAAA;MAAArE,KAAA,EAKA,SAAA+d,YAAY;QACR,OAAO,IAAI,CAAC5T,GAAG,CAAC,QAAQ,CAAC;;;;EAIjC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAAge,eAAe;QACX,OAAO,IAAI,CAAC7T,GAAG,CAAC,WAAW,CAAC;;;;EAIpC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAAie,aAAa;QACT,OAAO,IAAI,CAAC9T,GAAG,CAAC,SAAS,CAAC;;;;EAIlC;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;MALI9F,GAAA;MAAArE,KAAA,EAMA,SAAAke,WAAWC,OAAO,EAAE;QAChB,OAAO,IAAI,CAAC/T,GAAG,CAAC,SAAS,EAAE+T,OAAO,CAAC;;;;EAI3C;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9Z,GAAA;MAAArE,KAAA,EAKA,SAAAoe,gCAAgC;QAC5B,OAAO,IAAI,CAACjU,GAAG,CAAC,4BAA4B,CAAC;;;;EAIrD;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAAqe,yBAAyB;QACrB,OAAO,IAAI,CAAClU,GAAG,CAAC,qBAAqB,CAAC;;;;EAI9C;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAAse,cAAc;QACV,OAAO,IAAI,CAACnU,GAAG,CAAC,UAAU,CAAC;;;;EAInC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAAue,aAAa;QACT,OAAO,IAAI,CAACpU,GAAG,CAAC,SAAS,CAAC;;;;EAIlC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAAwe,gBAAgB;QACZ,OAAO,IAAI,CAACrU,GAAG,CAAC,YAAY,CAAC;;;;EAIrC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAAye,oBAAoB;QAChB,OAAO,IAAI,CAACtU,GAAG,CAAC,gBAAgB,CAAC;;;;EAIzC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAAwP,aAAa;QACT,OAAO,IAAI,CAACrF,GAAG,CAAC,SAAS,CAAC;;;;EAIlC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAA0e,gBAAgB;QACZ,OAAO,IAAI,CAACvU,GAAG,CAAC,YAAY,CAAC;;;;EAIrC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;MALI9F,GAAA;MAAArE,KAAA,EAMA,SAAA2e,gBAAgB;QACZ,OAAO,IAAI,CAACxU,GAAG,CAAC,YAAY,CAAC;;;;EAIrC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;MALI9F,GAAA;MAAArE,KAAA,EAMA,SAAA4e,iBAAiB;QACb,OAAO,IAAI,CAACzU,GAAG,CAAC,aAAa,CAAC;;;;EAItC;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;MAJI9F,GAAA;MAAArE,KAAA,EAKA,SAAA6e,cAAc;QACV,OAAO,IAAI,CAAC1U,GAAG,CAAC,UAAU,CAAC;;;;EAInC;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;MAPI9F,GAAA;MAAArE,KAAA,EAQA,SAAA8e,YAAY;QACR,OAAO,IAAI,CAAC3U,GAAG,CAAC,QAAQ,CAAC;;;;EAIjC;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MAVI9F,GAAA;MAAArE,KAAA,EAWA,SAAA+e,UAAUC,MAAM,EAAE;QACd,OAAO,IAAI,CAAC5U,GAAG,CAAC,QAAQ,EAAE4U,MAAM,CAAC;;;;;;;;EAQzC;EACA;EACA;EACA;EACA;EACA;EACA;;MAPI3a,GAAA;MAAArE,KAAA;QAAA,IAAAif,aAAA,GAAA7L,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,CAQA,SAAAC,QAAmBV,YAAY,EAAEE,OAAO;UAAA,IAAAmM,MAAA;UAAA,IAAAC,SAAA;UAAA,OAAA9L,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAApN,IAAA;cAAA;gBAAA,IAC/BqM,YAAY;kBAAAe,QAAA,CAAApN,IAAA;kBAAA;;gBAAA,MACP,IAAI5E,SAAS,CAAC,mCAAmC,CAAC;cAAA;gBAAAgS,QAAA,CAAApN,IAAA;gBAAA,OAGtD,IAAI,CAACmU,KAAK,EAAE;cAAA;gBACZwE,SAAS,GAAG,IAAI3M,kBAAkB,CAAC,IAAI,EAAEK,YAAY,EAAEE,OAAO,CAAC;gBACrExP,WAAW,CAAC,IAAI,EAAE,2BAA2B,CAAC;gBAC9C4b,SAAS,CAACrQ,gBAAgB,CAAC,YAAY,EAAE;kBAAA,OAAMvL,WAAW,CAAC2b,MAAI,EAAE,8BAA8B,CAAC;kBAAC;gBAAC,OAAAtL,QAAA,CAAAG,MAAA,WAE3FoL,SAAS;cAAA;cAAA;gBAAA,OAAAvL,QAAA,CAAAW,IAAA;;aAAAhB,OAAA;SACnB;QAAA,SAAA6L,aAAA5K,EAAA,EAAAC,GAAA;UAAA,OAAAwK,aAAA,CAAA9W,KAAA,OAAA5G,SAAA;;QAAA,OAAA6d,YAAA;;;MAAA/a,GAAA;MAAArE,KAAA,EAzqCD,SAAAM,aAAkBC,GAAG,EAAE;QACnB,OAAOD,UAAU,CAACC,GAAG,CAAC;;;IACzB,OAAA2O,MAAA;EAAA;EA2qCL,IAAI,CAAClQ,eAAe,EAAE;IAClB8R,UAAU,GAAGN,oBAAoB,EAAE;IACnC1C,gBAAgB,EAAE;IAClBM,YAAY,EAAE;IACdW,uBAAuB,EAAE;IACzBO,iBAAiB,EAAE;IACnBU,eAAe,EAAE;EACrB;;;;;;;;"}