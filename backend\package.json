{"name": "backend", "version": "1.0.0", "description": "", "type": "module", "main": "server.js", "scripts": {"start": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.1", "mongoose": "^7.5.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "nodemailer": "^6.9.4", "nodemon": "^3.0.1", "path": "^0.12.7", "razorpay": "^2.9.2"}}