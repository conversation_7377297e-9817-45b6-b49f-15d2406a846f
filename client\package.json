{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "axios": "^1.5.0", "chart.js": "^4.4.0", "daisyui": "^3.7.4", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.11.0", "react-redux": "^8.1.2", "react-router-dom": "^6.16.0", "react-slick": "^0.29.0", "react-youtube": "^10.1.0", "slick-carousel": "^1.8.1"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "eslint-plugin-simple-import-sort": "^10.0.0", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}}