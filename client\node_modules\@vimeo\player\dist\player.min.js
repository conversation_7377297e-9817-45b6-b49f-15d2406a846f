/*! @vimeo/player v2.29.2 | (c) 2025 Vimeo | MIT License | https://github.com/vimeo/player.js */
((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):((e="undefined"!=typeof globalThis?globalThis:e||self).Vimeo=e.Vimeo||{},e.Vimeo.Player=t())})(this,function(){function r(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function u(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach(function(e){f(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function P(){P=function(){return a};var a={},e=Object.prototype,c=e.hasOwnProperty,l=Object.defineProperty||function(e,t,n){e[t]=n.value},t="function"==typeof Symbol?Symbol:{},r=t.iterator||"@@iterator",n=t.asyncIterator||"@@asyncIterator",o=t.toStringTag||"@@toStringTag";function i(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{i({},"")}catch(e){i=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var o,i,a,u,t=t&&t.prototype instanceof d?t:d,t=Object.create(t.prototype),r=new k(r||[]);return l(t,"_invoke",{value:(o=e,i=n,a=r,u="suspendedStart",function(e,t){if("executing"===u)throw new Error("Generator is already running");if("completed"===u){if("throw"===e)throw t;return x()}for(a.method=e,a.arg=t;;){var n=a.delegate;if(n){n=function e(t,n){var r=n.method,o=t.iterator[r];if(void 0===o)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=void 0,e(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;r=s(o,t.iterator,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,f;o=r.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=void 0),n.delegate=null,f):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,f)}(n,a);if(n){if(n===f)continue;return n}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===u)throw u="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);u="executing";n=s(o,i,a);if("normal"===n.type){if(u=a.done?"completed":"suspendedYield",n.arg===f)continue;return{value:n.arg,done:a.done}}"throw"===n.type&&(u="completed",a.method="throw",a.arg=n.arg)}})}),t}function s(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}a.wrap=u;var f={};function d(){}function h(){}function p(){}var t={},y=(i(t,r,function(){return this}),Object.getPrototypeOf),y=y&&y(y(E([]))),v=(y&&y!==e&&c.call(y,r)&&(t=y),p.prototype=d.prototype=Object.create(t));function m(e){["next","throw","return"].forEach(function(t){i(e,t,function(e){return this._invoke(t,e)})})}function g(a,u){var t;l(this,"_invoke",{value:function(n,r){function e(){return new u(function(e,t){!function t(e,n,r,o){var i,e=s(a[e],a,n);if("throw"!==e.type)return(n=(i=e.arg).value)&&"object"==typeof n&&c.call(n,"__await")?u.resolve(n.__await).then(function(e){t("next",e,r,o)},function(e){t("throw",e,r,o)}):u.resolve(n).then(function(e){i.value=e,r(i)},function(e){return t("throw",e,r,o)});o(e.arg)}(n,r,e,t)})}return t=t?t.then(e,e):e()}})}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function b(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function E(t){if(t){var n,e=t[r];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length))return n=-1,(e=function e(){for(;++n<t.length;)if(c.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e}).next=e}return{next:x}}function x(){return{value:void 0,done:!0}}return l(v,"constructor",{value:h.prototype=p,configurable:!0}),l(p,"constructor",{value:h,configurable:!0}),h.displayName=i(p,o,"GeneratorFunction"),a.isGeneratorFunction=function(e){e="function"==typeof e&&e.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},a.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,i(e,o,"GeneratorFunction")),e.prototype=Object.create(v),e},a.awrap=function(e){return{__await:e}},m(g.prototype),i(g.prototype,n,function(){return this}),a.AsyncIterator=g,a.async=function(e,t,n,r,o){void 0===o&&(o=Promise);var i=new g(u(e,t,n,r),o);return a.isGeneratorFunction(t)?i:i.next().then(function(e){return e.done?e.value:i.next()})},m(v),i(v,o,"Generator"),i(v,r,function(){return this}),i(v,"toString",function(){return"[object Generator]"}),a.keys=function(e){var t,n=Object(e),r=[];for(t in n)r.push(t);return r.reverse(),function e(){for(;r.length;){var t=r.pop();if(t in n)return e.value=t,e.done=!1,e}return e.done=!0,e}},a.values=E,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!e)for(var t in this)"t"===t.charAt(0)&&c.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var r=this;function e(e,t){return i.type="throw",i.arg=n,r.next=e,t&&(r.method="next",r.arg=void 0),!!t}for(var t=this.tryEntries.length-1;0<=t;--t){var o=this.tryEntries[t],i=o.completion;if("root"===o.tryLoc)return e("end");if(o.tryLoc<=this.prev){var a=c.call(o,"catchLoc"),u=c.call(o,"finallyLoc");if(a&&u){if(this.prev<o.catchLoc)return e(o.catchLoc,!0);if(this.prev<o.finallyLoc)return e(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return e(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return e(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&c.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}var i=(o=o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc?null:o)?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,f):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),b(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n,r,o=this.tryEntries[t];if(o.tryLoc===e)return"throw"===(n=o.completion).type&&(r=n.arg,b(o)),r}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:E(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},a}function c(e,t,n,r,o,i,a){try{var u=e[i](a),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,o)}function d(u){return function(){var e=this,a=arguments;return new Promise(function(t,n){var r=u.apply(e,a);function o(e){c(r,t,n,o,i,"next",e)}function i(e){c(r,t,n,o,i,"throw",e)}o(void 0)})}}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,I(r.key),r)}}function s(e,t,n){t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1})}function f(e,t,n){(t=I(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}function i(e){return(i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function h(e,t){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function a(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function p(e,t,n){return(p=a()?Reflect.construct.bind():function(e,t,n){var r=[null];r.push.apply(r,t);t=new(Function.bind.apply(e,r));return n&&h(t,n.prototype),t}).apply(null,arguments)}function F(e){var n="function"==typeof Map?new Map:void 0;return function(e){if(null===e||-1===Function.toString.call(e).indexOf("[native code]"))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(e))return n.get(e);n.set(e,t)}function t(){return p(e,arguments,i(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),h(t,e)}(e)}function y(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function R(n){var r=a();return function(){var e,t=i(n),t=(e=r?(e=i(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),this);if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return y(t)}}function q(e,t){return(e=>{if(Array.isArray(e))return e})(e)||((e,t)=>{var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}})(e,t)||((e,t)=>{var n;if(e)return"string"==typeof e?v(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?v(e,t):void 0})(e,t)||(()=>{throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")})()}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function I(e){e=((e,t)=>{if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);if("object"!=typeof(n=n.call(e,t||"default")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")})(e,"string");return"symbol"==typeof e?e:String(e)}var e="undefined"!=typeof global&&"[object global]"==={}.toString.call(global),t="undefined"!=typeof Bun,n="undefined"!=typeof Deno,e=e||t||n;function V(e,t){return 0===e.indexOf(t.toLowerCase())?e:"".concat(t.toLowerCase()).concat(e.substr(0,1).toUpperCase()).concat(e.substr(1))}function m(e){return/^(https?:)?\/\/((((player|www)\.)?vimeo\.com)|((player\.)?[a-zA-Z0-9-]+\.(videoji\.(hk|cn)|vimeo\.work)))(?=$|\/)/.test(e)}function D(e){return/^https:\/\/player\.((vimeo\.com)|([a-zA-Z0-9-]+\.(videoji\.(hk|cn)|vimeo\.work)))\/video\/\d+/.test(e)}function z(e){var t,e=0<arguments.length&&void 0!==e?e:{},n=e.id,e=n||e.url;if(!e)throw new Error("An id or url must be passed, either in an options object or as a data-vimeo-id or data-vimeo-url attribute.");if(t=e,!isNaN(parseFloat(t))&&isFinite(t)&&Math.floor(t)==t)return"https://vimeo.com/".concat(e);if(m(e))return e.replace("http:","https:");if(n)throw new TypeError("“".concat(n,"” is not a valid video id."));throw new TypeError("“".concat(e,"” is not a vimeo.com url."))}function U(t,e,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:"addEventListener",o=4<arguments.length&&void 0!==arguments[4]?arguments[4]:"removeEventListener",i="string"==typeof e?[e]:e;return i.forEach(function(e){t[r](e,n)}),{cancel:function(){return i.forEach(function(e){return t[o](e,n)})}}}function g(e,t){t=1<arguments.length&&void 0!==t?t:document;if(e&&t&&"function"==typeof t.querySelectorAll)for(var n=t.querySelectorAll("iframe"),r=0;r<n.length;r++)if(n[r]&&n[r].contentWindow===e)return n[r];return null}t=void 0!==Array.prototype.indexOf,n="undefined"!=typeof window&&void 0!==window.postMessage;if(!(e||t&&n))throw new Error("Sorry, the Vimeo Player API is not available in this browser.");var W,B,w,G="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function b(){if(void 0===this)throw new TypeError("Constructor WeakMap requires 'new'");if(w(this,"_id","_WeakMap_"+Y()+"."+Y()),0<arguments.length)throw new TypeError("WeakMap iterable is not supported")}function k(e,t){if(!E(e)||!W.call(e,"_id"))throw new TypeError(t+" method called on incompatible receiver "+typeof e)}function Y(){return Math.random().toString().substring(2)}function E(e){return Object(e)===e}(t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:G).WeakMap||(W=Object.prototype.hasOwnProperty,B=Object.defineProperty&&(()=>{try{return 1===Object.defineProperty({},"x",{value:1}).x}catch(e){}})(),t.WeakMap=((w=function(e,t,n){B?Object.defineProperty(e,t,{configurable:!0,writable:!0,value:n}):e[t]=n})(b.prototype,"delete",function(e){var t;return k(this,"delete"),!!E(e)&&!(!(t=e[this._id])||t[0]!==e||(delete e[this._id],0))}),w(b.prototype,"get",function(e){var t;return k(this,"get"),E(e)&&(t=e[this._id])&&t[0]===e?t[1]:void 0}),w(b.prototype,"has",function(e){var t;return k(this,"has"),!!E(e)&&!(!(t=e[this._id])||t[0]!==e)}),w(b.prototype,"set",function(e,t){var n;if(k(this,"set"),E(e))return(n=e[this._id])&&n[0]===e?n[1]=t:w(e,this._id,[e,t]),this;throw new TypeError("Invalid value used as weak map key")}),w(b,"_polyfill",!0),b));(function(e){var t,n,r;r=function(){var t,n,r,o,i,e=Object.prototype.toString,a="undefined"!=typeof setImmediate?function(e){return setImmediate(e)}:setTimeout;try{Object.defineProperty({},"x",{}),t=function(e,t,n,r){return Object.defineProperty(e,t,{value:n,writable:!0,configurable:!1!==r})}}catch(e){t=function(e,t,n){return e[t]=n,e}}function u(e,t){this.fn=e,this.self=t,this.next=void 0}function c(e,t){m.add(e,t),n=n||a(m.drain)}function l(e){var t,n=typeof e;return"function"==typeof(t=null==e||"object"!=n&&"function"!=n?t:e.then)&&t}function s(){for(var e=0;e<this.chain.length;e++){n=t=i=o=r=void 0;var t,n,r=this,o=1===this.state?this.chain[e].success:this.chain[e].failure,i=this.chain[e];try{!1===o?i.reject(r.msg):(t=!0===o?r.msg:o.call(void 0,r.msg))===i.promise?i.reject(TypeError("Promise-chain cycle")):(n=l(t))?n.call(t,i.resolve,i.reject):i.resolve(t)}catch(e){i.reject(e)}}this.chain.length=0}function f(e){var n,r=this;if(!r.triggered){r.triggered=!0,r.def&&(r=r.def);try{(n=l(e))?c(function(){var t=new p(r);try{n.call(e,function(){f.apply(t,arguments)},function(){d.apply(t,arguments)})}catch(e){d.call(t,e)}}):(r.msg=e,r.state=1,0<r.chain.length&&c(s,r))}catch(e){d.call(new p(r),e)}}}function d(e){var t=this;t.triggered||(t.triggered=!0,(t=t.def?t.def:t).msg=e,t.state=2,0<t.chain.length&&c(s,t))}function h(e,n,r,o){for(var t=0;t<n.length;t++)(t=>{e.resolve(n[t]).then(function(e){r(t,e)},o)})(t)}function p(e){this.def=e,this.triggered=!1}function y(e){this.promise=e,this.state=0,this.triggered=!1,this.chain=[],this.msg=void 0}function v(e){if("function"!=typeof e)throw TypeError("Not a function");if(0!==this.__NPO__)throw TypeError("Not a promise");this.__NPO__=1;var r=new y(this);this.then=function(e,t){var n={success:"function"!=typeof e||e,failure:"function"==typeof t&&t};return n.promise=new this.constructor(function(e,t){if("function"!=typeof e||"function"!=typeof t)throw TypeError("Not a function");n.resolve=e,n.reject=t}),r.chain.push(n),0!==r.state&&c(s,r),n.promise},this.catch=function(e){return this.then(void 0,e)};try{e.call(void 0,function(e){f.call(r,e)},function(e){d.call(r,e)})}catch(e){d.call(r,e)}}var m={add:function(e,t){i=new u(e,t),o?o.next=i:r=i,o=i},drain:function(){var e=r;for(r=o=n=void 0;e;)e.fn.call(e.self),e=e.next}},g=t({},"constructor",v,!1);return t(v.prototype=g,"__NPO__",0,!1),t(v,"resolve",function(n){return n&&"object"==typeof n&&1===n.__NPO__?n:new this(function(e,t){if("function"!=typeof e||"function"!=typeof t)throw TypeError("Not a function");e(n)})}),t(v,"reject",function(n){return new this(function(e,t){if("function"!=typeof e||"function"!=typeof t)throw TypeError("Not a function");t(n)})}),t(v,"all",function(t){var a=this;return"[object Array]"!=e.call(t)?a.reject(TypeError("Not an array")):0===t.length?a.resolve([]):new a(function(n,e){if("function"!=typeof n||"function"!=typeof e)throw TypeError("Not a function");var r=t.length,o=Array(r),i=0;h(a,t,function(e,t){o[e]=t,++i===r&&n(o)},e)})}),t(v,"race",function(t){var r=this;return"[object Array]"!=e.call(t)?r.reject(TypeError("Not an array")):new r(function(n,e){if("function"!=typeof n||"function"!=typeof e)throw TypeError("Not a function");h(r,t,function(e,t){n(t)},e)})}),v},(n=G)[t="Promise"]=n[t]||r(),e.exports&&(e.exports=n[t])})(n={exports:{}});var x=n.exports,T=new WeakMap;function j(e,t,n){var r=T.get(e.element)||{};t in r||(r[t]=[]),r[t].push(n),T.set(e.element,r)}function _(e,t){return(T.get(e.element)||{})[t]||[]}function O(e,t,n){var r=T.get(e.element)||{};return!r[t]||(n?(-1!==(n=r[t].indexOf(n))&&r[t].splice(n,1),T.set(e.element,r),r[t]&&0===r[t].length):(r[t]=[],T.set(e.element,r),!0))}function S(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){return console.warn(e),{}}return e}function A(e,t,n){e.element.contentWindow&&e.element.contentWindow.postMessage&&(t={method:t},void 0!==n&&(t.value=n),8<=(n=parseFloat(navigator.userAgent.toLowerCase().replace(/^.*msie (\d+).*$/,"$1")))&&n<10&&(t=JSON.stringify(t)),e.element.contentWindow.postMessage(t,e.origin))}function H(n,r){var t,e,o,i,a=[];(r=S(r)).event?("error"===r.event&&_(n,r.data.method).forEach(function(e){var t=new Error(r.data.message);t.name=r.data.name,e.reject(t),O(n,r.data.method,e)}),a=_(n,"event:".concat(r.event)),t=r.data):r.method&&(e=n,o=r.method,o=!((i=_(e,o)).length<1)&&(O(e,o,e=i.shift()),e))&&(a.push(o),t=r.value),a.forEach(function(e){try{"function"==typeof e?e.call(n,t):e.resolve(t)}catch(e){}})}var Q=["airplay","audio_tracks","audiotrack","autopause","autoplay","background","byline","cc","chapter_id","chapters","chromecast","color","colors","controls","dnt","end_time","fullscreen","height","id","initial_quality","interactive_params","keyboard","loop","maxheight","max_quality","maxwidth","min_quality","muted","play_button_position","playsinline","portrait","preload","progress_bar","quality","quality_selector","responsive","skipping_forward","speed","start_time","texttrack","thumbnail_id","title","transcript","transparent","unmute_button","url","vimeo_logo","volume","watch_full_video","width"];function $(r,e){return Q.reduce(function(e,t){var n=r.getAttribute("data-vimeo-".concat(t));return!n&&""!==n||(e[t]=""===n?1:n),e},1<arguments.length&&void 0!==e?e:{})}function M(e,t){var n,e=e.html;if(t)return null===t.getAttribute("data-vimeo-initialized")&&((n=document.createElement("div")).innerHTML=e,t.appendChild(n.firstChild),t.setAttribute("data-vimeo-initialized","true")),t.querySelector("iframe");throw new TypeError("An element must be provided")}function J(a,e,t){var u=1<arguments.length&&void 0!==e?e:{},c=2<arguments.length?t:void 0;return new Promise(function(t,n){if(!m(a))throw new TypeError("“".concat(a,"” is not a vimeo.com url."));var e,r=(e=>{for(var t=((e=(e||"").match(/^(?:https?:)?(?:\/\/)?([^/?]+)/))&&e[1]||"").replace("player.",""),n=0,r=[".videoji.hk",".vimeo.work",".videoji.cn"];n<r.length;n++)if(t.endsWith(r[n]))return t;return"vimeo.com"})(a),o="https://".concat(r,"/api/oembed.json?url=").concat(encodeURIComponent(a));for(e in u)u.hasOwnProperty(e)&&(o+="&".concat(e,"=").concat(encodeURIComponent(u[e])));var i=new("XDomainRequest"in window?XDomainRequest:XMLHttpRequest);i.open("GET",o,!0),i.onload=function(){if(404===i.status)n(new Error("“".concat(a,"” was not found.")));else if(403===i.status)n(new Error("“".concat(a,"” is not embeddable.")));else try{var e=JSON.parse(i.responseText);403===e.domain_status_code?(M(e,c),n(new Error("“".concat(a,"” is not embeddable.")))):t(e)}catch(e){n(e)}},i.onerror=function(){var e=i.status?" (".concat(i.status,")"):"";n(new Error("There was an error fetching the embed code from Vimeo".concat(e,".")))},i.send()})}function X(e){function n(e){"console"in window&&console.error&&console.error("There was an error creating an embed: ".concat(e))}e=0<arguments.length&&void 0!==e?e:document,e=[].slice.call(e.querySelectorAll("[data-vimeo-id], [data-vimeo-url]"));e.forEach(function(t){try{var e;null===t.getAttribute("data-vimeo-defer")&&J(z(e=$(t)),e,t).then(function(e){return M(e,t)}).catch(n)}catch(e){n(e)}})}function Z(){var i=(()=>{for(var e,t=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],n=0,r=t.length,o={};n<r;n++)if((e=t[n])&&e[1]in document){for(n=0;n<e.length;n++)o[t[0][n]]=e[n];return o}return!1})(),n={fullscreenchange:i.fullscreenchange,fullscreenerror:i.fullscreenerror},a={request:function(o){return new Promise(function(e,t){function n(){a.off("fullscreenchange",n),e()}a.on("fullscreenchange",n);var r=(o=o||document.documentElement)[i.requestFullscreen]();r instanceof Promise&&r.then(n).catch(t)})},exit:function(){return new Promise(function(t,e){var n,r;a.isFullscreen?(a.on("fullscreenchange",n=function e(){a.off("fullscreenchange",e),t()}),(r=document[i.exitFullscreen]())instanceof Promise&&r.then(n).catch(e)):t()})},on:function(e,t){e=n[e];e&&document.addEventListener(e,t)},off:function(e,t){e=n[e];e&&document.removeEventListener(e,t)}};return Object.defineProperties(a,{isFullscreen:{get:function(){return Boolean(document[i.fullscreenElement])}},element:{enumerable:!0,get:function(){return document[i.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(document[i.fullscreenEnabled])}}}),a}var K={role:"viewer",autoPlayMuted:!0,allowedDrift:.3,maxAllowedDrift:1,minCheckInterval:.1,maxRateAdjustment:.2,maxTimeToCatchUp:1},ee=(e=>{var t=a;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&h(t,e);var r,n,o,i=R(a);function a(e,t){var o,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},r=3<arguments.length?arguments[3]:void 0;return l(this,a),f(y(o=i.call(this)),"logger",void 0),f(y(o),"speedAdjustment",0),f(y(o),"adjustSpeed",(()=>{var n=d(P().mark(function e(t,n){var r;return P().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(o.speedAdjustment===n)return e.abrupt("return");e.next=2;break;case 2:return e.next=4,t.getPlaybackRate();case 4:return e.t0=e.sent,e.t1=o.speedAdjustment,e.t2=e.t0-e.t1,e.t3=n,r=e.t2+e.t3,o.log("New playbackRate:  ".concat(r)),e.next=12,t.setPlaybackRate(r);case 12:o.speedAdjustment=n;case 13:case"end":return e.stop()}},e)}));return function(e,t){return n.apply(this,arguments)}})()),o.logger=r,o.init(t,e,u(u({},K),n)),o}return s(a,[{key:"disconnect",value:function(){this.dispatchEvent(new Event("disconnect"))}},{key:"init",value:(o=d(P().mark(function e(t,n,r){var o,i,a,u=this;return P().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.waitForTOReadyState(t,"open");case 2:if("viewer"===r.role)return e.next=5,this.updatePlayer(t,n,r);e.next=10;break;case 5:o=U(t,"change",function(){return u.updatePlayer(t,n,r)}),i=this.maintainPlaybackPosition(t,n,r),this.addEventListener("disconnect",function(){i.cancel(),o.cancel()}),e.next=14;break;case 10:return e.next=12,this.updateTimingObject(t,n);case 12:a=U(n,["seeked","play","pause","ratechange"],function(){return u.updateTimingObject(t,n)},"on","off"),this.addEventListener("disconnect",function(){return a.cancel()});case 14:case"end":return e.stop()}},e,this)})),function(e,t,n){return o.apply(this,arguments)})},{key:"updateTimingObject",value:(n=d(P().mark(function e(t,n){var r,o,i;return P().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Promise.all([n.getCurrentTime(),n.getPaused(),n.getPlaybackRate()]);case 2:i=e.sent,i=q(i,3),r=i[0],o=i[1],i=i[2],t.update({position:r,velocity:o?0:i});case 8:case"end":return e.stop()}},e)})),function(e,t){return n.apply(this,arguments)})},{key:"updatePlayer",value:(r=d(P().mark(function e(t,n,r){var o,i;return P().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i=t.query(),o=i.position,i=i.velocity,"number"==typeof o&&n.setCurrentTime(o),"number"!=typeof i)e.next=25;else{if(0===i)return e.next=6,n.getPaused();e.next=11}break;case 6:if(e.t0=e.sent,!1!==e.t0){e.next=9;break}n.pause();case 9:e.next=25;break;case 11:if(0<i)return e.next=14,n.getPaused();e.next=25;break;case 14:if(e.t1=e.sent,!0===e.t1)return e.next=18,n.play().catch((()=>{var t=d(P().mark(function e(t){return P().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("NotAllowedError"===t.name&&r.autoPlayMuted)return e.next=3,n.setMuted(!0);e.next=5;break;case 3:return e.next=5,n.play().catch(function(e){return n.allowLogging&&console.error("Couldn't play the video from TimingSrcConnector. Error:",e)});case 5:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}})());e.next=19;break;case 18:this.updatePlayer(t,n,r);case 19:return e.next=21,n.getPlaybackRate();case 21:if(e.t2=e.sent,e.t3=i,e.t2===e.t3){e.next=25;break}n.setPlaybackRate(i);case 25:case"end":return e.stop()}},e,this)})),function(e,t,n){return r.apply(this,arguments)})},{key:"maintainPlaybackPosition",value:function(i,a,e){var u=this,c=e.allowedDrift,l=e.maxAllowedDrift,s=e.maxRateAdjustment,f=e.maxTimeToCatchUp,e=1e3*Math.min(f,Math.max(e.minCheckInterval,l)),t=(()=>{var e=d(P().mark(function e(){var t,n,r,o;return P().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t0=0===i.query().velocity,e.t0){e.next=6;break}return e.next=4,a.getPaused();case 4:e.t1=e.sent,e.t0=!0===e.t1;case 6:if(e.t0)return e.abrupt("return");e.next=8;break;case 8:return e.t2=i.query().position,e.next=11,a.getCurrentTime();case 11:if(e.t3=e.sent,t=e.t2-e.t3,n=Math.abs(t),u.log("Drift: ".concat(t)),l<n)return e.next=18,u.adjustSpeed(a,0);e.next=22;break;case 18:a.setCurrentTime(i.query().position),u.log("Resync by currentTime"),e.next=29;break;case 22:if(c<n)return o=(o=n/f)<(r=s)?(r-o)/2:r,e.next=28,u.adjustSpeed(a,o*Math.sign(t));e.next=29;break;case 28:u.log("Resync by playbackRate");case 29:case"end":return e.stop()}},e)}));return function(){return e.apply(this,arguments)}})(),n=setInterval(function(){return t()},e);return{cancel:function(){return clearInterval(n)}}}},{key:"log",value:function(e){var t;null!=(t=this.logger)&&t.call(this,"TimingSrcConnector: ".concat(e))}},{key:"waitForTOReadyState",value:function(n,r){return new Promise(function(t){(function e(){n.readyState===r?t():n.addEventListener("readystatechange",e,{once:!0})})()})}}]),a})(F(EventTarget)),L=new WeakMap,C=new WeakMap,N={},Player=(()=>{function Player(i){var a=this,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(l(this,Player),this.allowLogging=t.logging||void 0===t.logging,window.jQuery&&i instanceof jQuery&&(1<i.length&&window.console&&console.warn&&this.allowLogging&&console.warn("A jQuery object with multiple elements was passed, using the first element."),i=i[0]),"undefined"!=typeof document&&"string"==typeof i&&(i=document.getElementById(i)),n=i,!Boolean(n&&1===n.nodeType&&"nodeName"in n&&n.ownerDocument&&n.ownerDocument.defaultView))throw new TypeError("You must pass either a valid element or a valid id.");if("IFRAME"===(i="IFRAME"!==i.nodeName&&(n=i.querySelector("iframe"))?n:i).nodeName&&!m(i.getAttribute("src")||""))throw new Error("The player element passed isn’t a Vimeo embed.");if(L.has(i))return L.get(i);this._window=i.ownerDocument.defaultView,this.element=i,this.origin="*";var e,n=new x(function(r,o){var e;a._onMessage=function(e){var t,n;m(e.origin)&&a.element.contentWindow===e.source&&("*"===a.origin&&(a.origin=e.origin),(e=S(e.data))&&"error"===e.event&&e.data&&"ready"===e.data.method?((t=new Error(e.data.message)).name=e.data.name,o(t)):(t=e&&"ready"===e.event,n=e&&"ping"===e.method,t||n?(a.element.setAttribute("data-ready","true"),r()):H(a,e)))},a._window.addEventListener("message",a._onMessage),"IFRAME"!==a.element.nodeName&&J(z(e=$(i,t)),e,i).then(function(e){var t,n,r=M(e,i);return a.element=r,a._originalElement=i,t=i,r=r,n=T.get(t),T.set(r,n),T.delete(t),L.set(a.element,a),e}).catch(o)});return C.set(this,n),L.set(this.element,this),"IFRAME"===this.element.nodeName&&A(this,"ping"),N.isEnabled&&(e=function(){return N.exit()},this.fullscreenchangeHandler=function(){(N.isFullscreen?j:O)(a,"event:exitFullscreen",e),a.ready().then(function(){A(a,"fullscreenchange",N.isFullscreen)})},N.on("fullscreenchange",this.fullscreenchangeHandler)),this.allowLogging&&console.log("\n%cVimeo is looking for feedback!\n%cComplete our survey about the Player SDK: https://t.maze.co/393567477","color:#00adef;font-size:1.2em;","color:#aaa;font-size:0.8em;"),this}var n;return s(Player,[{key:"callMethod",value:function(n){for(var r=this,e=arguments.length,o=new Array(1<e?e-1:0),t=1;t<e;t++)o[t-1]=arguments[t];if(null==n)throw new TypeError("You must pass a method name.");return new x(function(e,t){return r.ready().then(function(){j(r,n,{resolve:e,reject:t}),0===o.length?o={}:1===o.length&&(o=o[0]),A(r,n,o)}).catch(t)})}},{key:"get",value:function(n){var r=this;return new x(function(e,t){return n=V(n,"get"),r.ready().then(function(){j(r,n,{resolve:e,reject:t}),A(r,n)}).catch(t)})}},{key:"set",value:function(n,r){var o=this;return new x(function(e,t){if(n=V(n,"set"),null==r)throw new TypeError("There must be a value to set.");return o.ready().then(function(){j(o,n,{resolve:e,reject:t}),A(o,n,r)}).catch(t)})}},{key:"on",value:function(e,t){if(!e)throw new TypeError("You must pass an event name.");if(!t)throw new TypeError("You must pass a callback function.");if("function"!=typeof t)throw new TypeError("The callback must be a function.");0===_(this,"event:".concat(e)).length&&this.callMethod("addEventListener",e).catch(function(){}),j(this,"event:".concat(e),t)}},{key:"off",value:function(e,t){if(!e)throw new TypeError("You must pass an event name.");if(t&&"function"!=typeof t)throw new TypeError("The callback must be a function.");O(this,"event:".concat(e),t)&&this.callMethod("removeEventListener",e).catch(function(e){})}},{key:"loadVideo",value:function(e){return this.callMethod("loadVideo",e)}},{key:"ready",value:function(){var e=C.get(this)||new x(function(e,t){t(new Error("Unknown player. Probably unloaded."))});return x.resolve(e)}},{key:"addCuePoint",value:function(e){return this.callMethod("addCuePoint",{time:e,data:1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}})}},{key:"removeCuePoint",value:function(e){return this.callMethod("removeCuePoint",e)}},{key:"enableTextTrack",value:function(e,t){if(e)return this.callMethod("enableTextTrack",{language:e,kind:t});throw new TypeError("You must pass a language.")}},{key:"disableTextTrack",value:function(){return this.callMethod("disableTextTrack")}},{key:"pause",value:function(){return this.callMethod("pause")}},{key:"play",value:function(){return this.callMethod("play")}},{key:"requestFullscreen",value:function(){return N.isEnabled?N.request(this.element):this.callMethod("requestFullscreen")}},{key:"exitFullscreen",value:function(){return N.isEnabled?N.exit():this.callMethod("exitFullscreen")}},{key:"getFullscreen",value:function(){return N.isEnabled?x.resolve(N.isFullscreen):this.get("fullscreen")}},{key:"requestPictureInPicture",value:function(){return this.callMethod("requestPictureInPicture")}},{key:"exitPictureInPicture",value:function(){return this.callMethod("exitPictureInPicture")}},{key:"getPictureInPicture",value:function(){return this.get("pictureInPicture")}},{key:"remotePlaybackPrompt",value:function(){return this.callMethod("remotePlaybackPrompt")}},{key:"unload",value:function(){return this.callMethod("unload")}},{key:"destroy",value:function(){var n=this;return new x(function(e){var t;C.delete(n),L.delete(n.element),n._originalElement&&(L.delete(n._originalElement),n._originalElement.removeAttribute("data-vimeo-initialized")),n.element&&"IFRAME"===n.element.nodeName&&n.element.parentNode&&(n.element.parentNode.parentNode&&n._originalElement&&n._originalElement!==n.element.parentNode?n.element.parentNode.parentNode.removeChild(n.element.parentNode):n.element.parentNode.removeChild(n.element)),n.element&&"DIV"===n.element.nodeName&&n.element.parentNode&&(n.element.removeAttribute("data-vimeo-initialized"),t=n.element.querySelector("iframe"))&&t.parentNode&&(t.parentNode.parentNode&&n._originalElement&&n._originalElement!==t.parentNode?t.parentNode.parentNode.removeChild(t.parentNode):t.parentNode.removeChild(t)),n._window.removeEventListener("message",n._onMessage),N.isEnabled&&N.off("fullscreenchange",n.fullscreenchangeHandler),e()})}},{key:"getAutopause",value:function(){return this.get("autopause")}},{key:"setAutopause",value:function(e){return this.set("autopause",e)}},{key:"getBuffered",value:function(){return this.get("buffered")}},{key:"getCameraProps",value:function(){return this.get("cameraProps")}},{key:"setCameraProps",value:function(e){return this.set("cameraProps",e)}},{key:"getChapters",value:function(){return this.get("chapters")}},{key:"getCurrentChapter",value:function(){return this.get("currentChapter")}},{key:"getColor",value:function(){return this.get("color")}},{key:"getColors",value:function(){return x.all([this.get("colorOne"),this.get("colorTwo"),this.get("colorThree"),this.get("colorFour")])}},{key:"setColor",value:function(e){return this.set("color",e)}},{key:"setColors",value:function(e){var t;return Array.isArray(e)?(t=new x(function(e){return e(null)}),e=[e[0]?this.set("colorOne",e[0]):t,e[1]?this.set("colorTwo",e[1]):t,e[2]?this.set("colorThree",e[2]):t,e[3]?this.set("colorFour",e[3]):t],x.all(e)):new x(function(e,t){return t(new TypeError("Argument must be an array."))})}},{key:"getCuePoints",value:function(){return this.get("cuePoints")}},{key:"getCurrentTime",value:function(){return this.get("currentTime")}},{key:"setCurrentTime",value:function(e){return this.set("currentTime",e)}},{key:"getDuration",value:function(){return this.get("duration")}},{key:"getEnded",value:function(){return this.get("ended")}},{key:"getLoop",value:function(){return this.get("loop")}},{key:"setLoop",value:function(e){return this.set("loop",e)}},{key:"setMuted",value:function(e){return this.set("muted",e)}},{key:"getMuted",value:function(){return this.get("muted")}},{key:"getPaused",value:function(){return this.get("paused")}},{key:"getPlaybackRate",value:function(){return this.get("playbackRate")}},{key:"setPlaybackRate",value:function(e){return this.set("playbackRate",e)}},{key:"getPlayed",value:function(){return this.get("played")}},{key:"getQualities",value:function(){return this.get("qualities")}},{key:"getQuality",value:function(){return this.get("quality")}},{key:"setQuality",value:function(e){return this.set("quality",e)}},{key:"getRemotePlaybackAvailability",value:function(){return this.get("remotePlaybackAvailability")}},{key:"getRemotePlaybackState",value:function(){return this.get("remotePlaybackState")}},{key:"getSeekable",value:function(){return this.get("seekable")}},{key:"getSeeking",value:function(){return this.get("seeking")}},{key:"getTextTracks",value:function(){return this.get("textTracks")}},{key:"getVideoEmbedCode",value:function(){return this.get("videoEmbedCode")}},{key:"getVideoId",value:function(){return this.get("videoId")}},{key:"getVideoTitle",value:function(){return this.get("videoTitle")}},{key:"getVideoWidth",value:function(){return this.get("videoWidth")}},{key:"getVideoHeight",value:function(){return this.get("videoHeight")}},{key:"getVideoUrl",value:function(){return this.get("videoUrl")}},{key:"getVolume",value:function(){return this.get("volume")}},{key:"setVolume",value:function(e){return this.set("volume",e)}},{key:"setTimingSrc",value:(n=d(P().mark(function e(t,n){var r,o=this;return P().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}throw new TypeError("A Timing Object must be provided.");case 2:return e.next=4,this.ready();case 4:return r=new ee(this,t,n),A(this,"notifyTimingObjectConnect"),r.addEventListener("disconnect",function(){return A(o,"notifyTimingObjectDisconnect")}),e.abrupt("return",r);case 8:case"end":return e.stop()}},e,this)})),function(e,t){return n.apply(this,arguments)})}],[{key:"isVimeoUrl",value:m}]),Player})();return e||(N=Z(),X(),function(e){var n=0<arguments.length&&void 0!==e?e:document;window.VimeoPlayerResizeEmbeds_||(window.VimeoPlayerResizeEmbeds_=!0,window.addEventListener("message",function(e){var t;m(e.origin)&&e.data&&"spacechange"===e.data.event&&(t=e.source?g(e.source,n):null)&&(t.parentElement.style.paddingBottom="".concat(e.data.data[0].bottom,"px"))}))}(),function(e){var n=0<arguments.length&&void 0!==e?e:document;window.VimeoSeoMetadataAppended||(window.VimeoSeoMetadataAppended=!0,window.addEventListener("message",function(e){var t;m(e.origin)&&(t=S(e.data))&&"ready"===t.event&&(t=e.source?g(e.source,n):null)&&D(t.src)&&new Player(t).callMethod("appendVideoMetadata",window.location.href)}))}(),function(e){var r,o=0<arguments.length&&void 0!==e?e:document;window.VimeoCheckedUrlTimeParam||(window.VimeoCheckedUrlTimeParam=!0,r=function(e){"console"in window&&console.error&&console.error("There was an error getting video Id: ".concat(e))},window.addEventListener("message",function(e){var t,n;m(e.origin)&&(t=S(e.data))&&"ready"===t.event&&(t=e.source?g(e.source,o):null)&&D(t.src)&&(n=new Player(t)).getVideoId().then(function(e){var e=new RegExp("[?&]vimeo_t_".concat(e,"=([^&#]*)")).exec(window.location.href);e&&e[1]&&(e=decodeURI(e[1]),n.setCurrentTime(e))}).catch(r)}))}(),window.VimeoDRMEmbedsUpdated)||(window.VimeoDRMEmbedsUpdated=!0,window.addEventListener("message",function(e){var t;m(e.origin)&&(t=S(e.data))&&"drminitfailed"===t.event&&(t=e.source?g(e.source):null)&&!(e=t.getAttribute("allow")||"").includes("encrypted-media")&&(t.setAttribute("allow","".concat(e,"; encrypted-media")),(e=new URL(t.getAttribute("src"))).searchParams.set("forcereload","drm"),t.setAttribute("src",e.toString()))})),Player});
