{"name": "native-promise-only", "version": "0.8.1", "description": "Native Promise Only: A polyfill for native ES6 Promises **only**, nothing else.", "main": "./lib/npo.src.js", "scripts": {"test": "promises-aplus-tests test_adapter.js", "build": "./build.js"}, "devDependencies": {"promises-aplus-tests": "latest", "uglify-js": "~2.4.8"}, "repository": {"type": "git", "url": "git://github.com/getify/native-promise-only.git"}, "keywords": ["ES6", "Promise", "async", "promises-aplus"], "bugs": {"url": "https://github.com/getify/native-promise-only/issues", "email": "<EMAIL>"}, "homepage": "http://github.com/getify/native-promise-only", "author": "<PERSON> <<EMAIL>>", "license": "MIT"}